#!/usr/bin/env python3
"""
Task 5: Enhanced Web Scraping for Product Images
Scrapes multiple product images from Amazon using Selenium and Chrome WebDriver.
Supports different product categories and filters out icons/non-product images.
Stores stockcode, product_name, and image path in the CSV for CNN model training.
"""

import requests
import os
import pandas as pd
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import StaleElementReferenceException, NoSuchElementException

# --- CONFIGURABLE ---
MAX_IMAGES_PER_PRODUCT = 5  # Number of images to download per product
PRODUCTS_TO_SCRAPE = [
    "laptop", "smartphone", "headphones", "camera", "tablet",
    "watch", "shoes", "bag", "sunglasses", "books",
    "kitchen appliances", "home decor", "toys", "clothing", "electronics"
]

# --- UTILS ---
def create_directory(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def download_image(url, save_path):
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        with open(save_path, 'wb') as file:
            file.write(response.content)
        print(f"✓ Downloaded: {save_path}")
        return True
    except Exception as e:
        print(f"✗ Failed to download {url}: {e}")
        return False

def is_product_image(img_url, alt_text=None):
    """
    Enhanced filter to identify actual product images and exclude icons, UI elements, etc.
    """
    # Filter out icons, logos, sprites, banners, placeholders, arrows, nav, etc.
    bad_keywords = [
        'logo', 'icon', 'sprite', 'arrow', 'nav', 'banner', 'placeholder', 'blank',
        'transparent', 'favicon', 'cart', 'search', 'promo', 'giftcard', 'prime',
        'signin', 'avatar', 'profile', 'footer', 'header', 'star', 'rating', 'review',
        'badge', 'deal', 'wishlist', 'compare', 'heart', 'tick', 'check', 'close',
        'cross', 'hamburger', 'menu', 'dropdown', 'flag', 'button', 'btn', 'ui',
        'loading', 'spinner', 'loader', 'thumbnail_', 'thumb_', 'small_', 'tiny_',
        'pixel', '1x1', 'spacer', 'divider', 'separator', 'border', 'shadow'
    ]

    if not img_url:
        return False

    url_l = img_url.lower()

    # Check for bad keywords in URL
    if any(bad in url_l for bad in bad_keywords):
        return False

    # Check for bad keywords in alt text
    if alt_text and any(bad in alt_text.lower() for bad in bad_keywords):
        return False

    # Must have valid image extension
    valid_extensions = ['.jpg', '.jpeg', '.png', '.webp']
    if not any(ext in url_l for ext in valid_extensions):
        return False

    # Must be from Amazon CDN or contain product-related terms
    valid_sources = ['amazon', 'ssl-images-amazon', 'm.media-amazon']
    if not any(source in url_l for source in valid_sources):
        return False

    # Check minimum size indicators (avoid tiny images)
    size_indicators = ['_SL', '_AC_', '_UL', '_SR']
    has_size = any(indicator in img_url for indicator in size_indicators)

    # If it has size indicators, check if it's reasonably sized
    if has_size:
        # Extract size numbers and check if they're reasonable
        import re
        size_matches = re.findall(r'_(?:SL|AC_|UL|SR)(\d+)', img_url)
        if size_matches:
            max_size = max(int(size) for size in size_matches)
            if max_size < 100:  # Too small, likely a thumbnail or icon
                return False

    return True

def scrape_images_with_selenium(search_term="electronics", output_dir="data/scraped_images", start_stock_code=20000, num_products=10):
    """
    Enhanced scraping function with better error handling and product image filtering.
    """
    print(f"Scraping images for: {search_term}")
    create_directory(output_dir)
    create_directory("data/dataset")
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    try:
        # Use webdriver-manager for automatic driver management
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except ImportError:
            # Fallback to default Chrome driver (must be in PATH)
            driver = webdriver.Chrome(options=chrome_options)
        print("✓ Chrome WebDriver initialized")
        url = "https://www.amazon.com/"
        driver.get(url)
        print(f"✓ Navigated to: {url}")
        time.sleep(5)
        # --- Search logic (robust selectors) ---
        try:
            time.sleep(5)
            search_selectors = [
                "//input[@id='twotabsearchtextbox']",
                "//input[@name='field-keywords']",
                "//input[@type='text' and contains(@placeholder, 'Search')]",
                "//input[contains(@class, 'search')]"
            ]
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = driver.find_element(By.XPATH, selector)
                    print(f"✓ Found search box with selector: {selector}")
                    break
                except NoSuchElementException:
                    continue
            if not search_box:
                print("✗ Could not find search box with any selector")
                search_url = f"https://www.amazon.com/s?k={search_term.replace(' ', '+')}"
                driver.get(search_url)
                print(f"✓ Navigated directly to search results: {search_url}")
                time.sleep(5)
            else:
                search_box.clear()
                search_box.send_keys(search_term)
                print(f"✓ Entered search term: {search_term}")
                try:
                    search_button = driver.find_element(By.XPATH, "//input[@id='nav-search-submit-button']")
                    search_button.click()
                    print("✓ Clicked search button")
                except NoSuchElementException:
                    search_box.send_keys(Keys.RETURN)
                    print("✓ Pressed Enter to search")
                time.sleep(5)
        except Exception as e:
            print(f"✗ Error during search: {e}")
            try:
                search_url = f"https://www.amazon.com/s?k={search_term.replace(' ', '+')}"
                driver.get(search_url)
                print(f"✓ Fallback: Navigated directly to search results: {search_url}")
                time.sleep(5)
            except Exception as fallback_error:
                print(f"✗ Fallback navigation also failed: {fallback_error}")
                driver.quit()
                return []
        # --- Scrape product blocks ---
        product_blocks = driver.find_elements(By.XPATH, "//div[contains(@data-component-type, 's-search-result')]")
        print(f"✓ Found {len(product_blocks)} product blocks")
        csv_data = []
        total_downloaded = 0
        stock_code = start_stock_code
        for block in product_blocks:
            if stock_code >= start_stock_code + num_products:
                break
            # Get product name
            try:
                title_elem = block.find_element(By.XPATH, ".//span[@class='a-size-medium a-color-base a-text-normal']")
                product_name = title_elem.text.strip()
            except Exception:
                product_name = ""
            # Get all images in this block
            img_elements = block.find_elements(By.TAG_NAME, "img")
            product_imgs = []
            for img in img_elements:
                try:
                    img_url = img.get_attribute('src')
                    alt_text = img.get_attribute('alt')
                    if is_product_image(img_url, alt_text):
                        product_imgs.append(img_url)
                except Exception:
                    continue
            # Remove duplicates, keep up to MAX_IMAGES_PER_PRODUCT
            unique_imgs = []
            for u in product_imgs:
                if u not in unique_imgs:
                    unique_imgs.append(u)
            unique_imgs = unique_imgs[:MAX_IMAGES_PER_PRODUCT]
            if not unique_imgs:
                print(f"No valid product images found for {product_name} (stock code {stock_code})")
                continue
            # Download images
            stock_dir = os.path.join(output_dir, str(stock_code))
            create_directory(stock_dir)
            for idx, img_url in enumerate(unique_imgs):
                filename = f"{stock_code}_{idx}.jpg"
                save_path = os.path.join(stock_dir, filename)
                if download_image(img_url, save_path):
                    relative_path = os.path.relpath(save_path, start=os.getcwd())
                    csv_data.append({
                        'stockcode': str(stock_code),
                        'product_name': product_name,
                        'image': relative_path
                    })
                    total_downloaded += 1
            print(f"✓ {len(unique_imgs)} images saved for {product_name} (stock code {stock_code})")
            stock_code += 1
            time.sleep(0.5)
        driver.quit()
        print("✓ WebDriver closed")
        # Save to CSV
        df = pd.DataFrame(csv_data)
        csv_path = "data/dataset/CNN_Model_Train_Data.csv"
        df.to_csv(csv_path, index=False)
        print(f"\n=== SCRAPING COMPLETED ===")
        print(f"Total products processed: {stock_code - start_stock_code}")
        print(f"Successful downloads: {total_downloaded}")
        print(f"CSV saved to: {csv_path}")
        print(f"Images saved to: {output_dir}")
        print(f"\nSample CSV data:")
        print(df.head().to_string(index=False))
        return total_downloaded
    except Exception as e:
        print(f"✗ Error with WebDriver: {e}")
        return []

def scrape_multiple_categories(categories=None, output_dir="data/scraped_images",
                             start_stock_code=20000, products_per_category=10):
    """
    Scrape images for multiple product categories.

    Args:
        categories: List of product categories to scrape
        output_dir: Directory to save images
        start_stock_code: Starting stock code number
        products_per_category: Number of products to scrape per category

    Returns:
        Total number of images downloaded
    """
    if categories is None:
        categories = PRODUCTS_TO_SCRAPE

    print("=" * 80)
    print("🚀 ENHANCED MULTI-CATEGORY PRODUCT IMAGE SCRAPER")
    print("=" * 80)
    print(f"Categories to scrape: {len(categories)}")
    print(f"Products per category: {products_per_category}")
    print(f"Starting stock code: {start_stock_code}")
    print("=" * 80)

    total_downloaded = 0
    current_stock_code = start_stock_code

    # Create master CSV to combine all data
    all_csv_data = []

    for i, category in enumerate(categories, 1):
        print(f"\n[{i}/{len(categories)}] Processing category: {category}")
        print("-" * 50)

        try:
            # Create category-specific directory
            category_dir = os.path.join(output_dir, category.replace(" ", "_"))

            # Scrape this category
            downloaded = scrape_images_with_selenium(
                search_term=category,
                output_dir=category_dir,
                start_stock_code=current_stock_code,
                num_products=products_per_category
            )

            total_downloaded += downloaded
            current_stock_code += products_per_category

            print(f"✓ Category '{category}' completed: {downloaded} images downloaded")

            # Add delay between categories to be respectful
            if i < len(categories):
                print("⏳ Waiting 10 seconds before next category...")
                time.sleep(10)

        except Exception as e:
            print(f"✗ Error processing category '{category}': {e}")
            continue

    print("\n" + "=" * 80)
    print("📊 SCRAPING SUMMARY")
    print("=" * 80)
    print(f"Categories processed: {len(categories)}")
    print(f"Total images downloaded: {total_downloaded}")
    print(f"Images saved to: {output_dir}")
    print(f"CSV data saved to: data/dataset/CNN_Model_Train_Data.csv")
    print("=" * 80)

    return total_downloaded

if __name__ == "__main__":
    # Configuration
    START_STOCK_CODE = 20000
    PRODUCTS_PER_CATEGORY = 8  # Reduced for testing, increase for production

    # Choose scraping mode
    SCRAPE_MODE = "multi"  # "single" or "multi"

    if SCRAPE_MODE == "single":
        # Single category scraping (for testing)
        print("=" * 60)
        print("🚀 SINGLE CATEGORY SCRAPING MODE")
        print("=" * 60)
        scrape_images_with_selenium(
            search_term="laptop",  # Change this to any product
            output_dir="data/scraped_images",
            start_stock_code=START_STOCK_CODE,
            num_products=PRODUCTS_PER_CATEGORY
        )
    else:
        # Multi-category scraping (for production)
        print("🚀 MULTI-CATEGORY SCRAPING MODE")

        # You can customize the categories here
        custom_categories = [
            "laptop", "smartphone", "headphones", "camera", "tablet",
            "smartwatch", "wireless earbuds", "gaming mouse", "keyboard", "monitor"
        ]

        scrape_multiple_categories(
            categories=custom_categories,
            output_dir="data/scraped_images",
            start_stock_code=START_STOCK_CODE,
            products_per_category=PRODUCTS_PER_CATEGORY
        )