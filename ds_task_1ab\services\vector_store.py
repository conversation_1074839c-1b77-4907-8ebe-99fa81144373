import os
from pinecone import Pinecone, ServerlessSpec
from dotenv import load_dotenv

class VectorStore:
    def __init__(self, index_name="product-search", metric='cosine'):
        """Initialize connection to Pinecone vector database.
        
        Args:
            index_name (str): Name of the Pinecone index to use.
            metric (str): Similarity metric to use. Options:
                - 'cosine': Cosine similarity (default, best for text)
                - 'euclidean': L2 distance (good for images/audio)
                - 'dotproduct': Dot product (when magnitude matters)
        """
        # Load environment variables
        load_dotenv()
        
        # Validate metric choice
        valid_metrics = ['cosine', 'euclidean', 'dotproduct']
        if metric not in valid_metrics:
            raise ValueError(f"Invalid metric. Choose from: {valid_metrics}")
        
        # Initialize Pinecone
        api_key = os.getenv('PINECONE_API_KEY')
        
        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable is not set")
            
        # Create Pinecone client
        self.pc = Pinecone(api_key=api_key)
        
        try:
            print("Checking if index exists...")
            indexes = self.pc.list_indexes()
            print(f"Available indexes: {indexes.names()}")
            
            if index_name not in indexes.names():
                print(f"Creating new index: {index_name}")
                # Create serverless index with optimal configuration
                # Create serverless index for free tier
                spec = ServerlessSpec(cloud='aws', region='us-east-1')
                self.pc.create_index(
                    name=index_name,
                    dimension=384,  # dimension for all-MiniLM-L6-v2 model
                    metric=metric,
                    spec=spec
                )
                print("Index created successfully")
            else:
                print(f"Using existing index: {index_name}")
            
            # Get index instance
            self.index = self.pc.Index(index_name)
            self.metric = metric
            print("Vector store initialized successfully")
            
        except Exception as e:
            raise Exception(f"Failed to initialize Pinecone index: {str(e)}")
    
    def upsert_vectors(self, vectors, metadata):
        """Insert or update vectors in the database.
        
        Args:
            vectors (list): List of vectors to insert/update
            metadata (list): List of metadata dictionaries for each vector
        """
        try:
            if len(vectors) == 0 or len(metadata) == 0:
                raise ValueError("Vectors and metadata cannot be empty")
                
            if len(vectors) != len(metadata):
                raise ValueError("Number of vectors must match number of metadata entries")
            
            # Create vector IDs if not provided
            ids = [f"prod_{i}" for i in range(len(vectors))]
            
            # Create vectors list in the format expected by Pinecone
            vectors_list = [
                (id, vector, meta)
                for id, vector, meta in zip(ids, vectors, metadata)
            ]
            
            # Upsert in batches of 100
            batch_size = 100
            for i in range(0, len(vectors_list), batch_size):
                batch = vectors_list[i:i + batch_size]
                self.index.upsert(vectors=batch)
                
        except Exception as e:
            raise Exception(f"Failed to upsert vectors: {str(e)}")
    
    def search(self, query_vector, top_k=None, filter_dict=None, score_threshold=None):
        """Search for similar vectors with optional metadata filtering.
        
        Args:
            query_vector (list): Query vector to search for
            top_k (int, optional): Maximum number of results to return. If None, returns up to 100 results.
            filter_dict (dict, optional): Metadata filters to apply
                Example: {"price_range": {"$gte": 10, "$lte": 50}}
            score_threshold (float, optional): Minimum similarity score threshold
                - For cosine: typically 0.7 to 0.9 for good matches
                - For euclidean: lower values are better, threshold depends on data
                - For dotproduct: threshold depends on vector magnitudes
            
        Returns:
            list: List of (id, score, metadata) tuples for the most similar vectors
        """
        try:
            # Use a reasonable default if top_k is not specified
            search_top_k = top_k if top_k is not None else 100
            
            results = self.index.query(
                vector=query_vector,
                top_k=search_top_k,
                include_metadata=True,
                filter=filter_dict
            )
            
            matches = [(match.id, match.score, match.metadata) for match in results.matches]
            
            # Apply score threshold if provided
            if score_threshold is not None:
                if self.metric == 'euclidean':
                    # For euclidean, lower scores are better
                    matches = [(id, score, meta) for id, score, meta in matches if score <= score_threshold]
                else:
                    # For cosine and dotproduct, higher scores are better
                    matches = [(id, score, meta) for id, score, meta in matches if score >= score_threshold]
            
            return matches
            
        except Exception as e:
            raise Exception(f"Failed to search vectors: {str(e)}")
            
    def interpret_similarity_score(self, score):
        """Interpret the similarity score based on the metric being used.
        
        Args:
            score (float): The similarity score to interpret
            
        Returns:
            str: Human-readable interpretation of the score
        """
        if self.metric == 'cosine':
            if score > 0.9:
                return "Very Strong Match"
            elif score > 0.8:
                return "Strong Match"
            elif score > 0.7:
                return "Moderate Match"
            elif score > 0.5:
                return "Weak Match"
            else:
                return "Poor Match"
                
        elif self.metric == 'euclidean':
            # For euclidean, lower is better
            if score < 0.5:
                return "Very Strong Match"
            elif score < 1.0:
                return "Strong Match"
            elif score < 1.5:
                return "Moderate Match"
            elif score < 2.0:
                return "Weak Match"
            else:
                return "Poor Match"
                
        else:  # dotproduct
            # Interpretation depends on vector magnitudes
            # These thresholds should be adjusted based on your data
            if score > 0.8:
                return "Very Strong Match"
            elif score > 0.6:
                return "Strong Match"
            elif score > 0.4:
                return "Moderate Match"
            elif score > 0.2:
                return "Weak Match"
            else:
                return "Poor Match" 