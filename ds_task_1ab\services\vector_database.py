import os
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from pinecone import Pinecone, ServerlessSpec
from .similarity_metrics import SimilarityMetricsService
from .embedding_service import EmbeddingService
import logging

# Load environment variables
load_dotenv()

class VectorDatabaseService:
    """
    Class-based implementation for Pinecone vector database operations.
    Handles product vector storage, retrieval, and similarity search using modern embeddings.
    """
    
    def __init__(self, index_name: str = "product-recommendations"):
        """
        Initialize the vector database service.
        
        Args:
            index_name (str): Name of the Pinecone index
        """
        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.index_name = index_name
        self.api_key = os.getenv('PINECONE_API_KEY')
        
        if not self.api_key:
            raise ValueError("PINECONE_API_KEY not found in environment variables")
        
        # Initialize Pinecone with new API
        self.pc = Pinecone(api_key=self.api_key)
        
        # Initialize embedding service
        self.embedding_service = EmbeddingService('all-MiniLM-L6-v2')
        
        # Initialize similarity metrics service
        self.similarity_service = SimilarityMetricsService()
        
        self.index = None
        self._setup_index()
    
    def _setup_index(self):
        """
        Set up the Pinecone index with proper configuration.
        """
        try:
            # Check if index already exists
            indexes = self.pc.list_indexes()
            self.logger.info(f"Available indexes: {indexes.names()}")
            
            if self.index_name not in indexes.names():
                self.logger.info(f"Creating new index: {self.index_name}")
                # Create new index with appropriate configuration for all-MiniLM-L6-v2
                dimension = self.embedding_service.get_embedding_dimension()
                spec = ServerlessSpec(cloud='aws', region='us-east-1')
                self.pc.create_index(
                    name=self.index_name,
                    dimension=dimension,  # Dynamic dimension from embedding service
                    metric='cosine',
                    spec=spec
                )
                self.logger.info("Index created successfully")
            else:
                self.logger.info(f"Using existing index: {self.index_name}")
            
            # Get index instance
            self.index = self.pc.Index(self.index_name)
            self.logger.info("Vector store initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Error setting up Pinecone index: {str(e)}")
            raise
    
    def _prepare_product_text(self, product_data: Dict[str, Any]) -> str:
        """
        Prepare product text for vectorization by combining relevant fields.
        
        Args:
            product_data (Dict): Product data dictionary
            
        Returns:
            str: Combined text for vectorization
        """
        text_fields = []
        
        # Add relevant text fields (adjust based on your dataset structure)
        if 'name' in product_data:
            text_fields.append(str(product_data['name']))
        if 'description' in product_data:
            text_fields.append(str(product_data['description']))
        if 'category' in product_data:
            text_fields.append(str(product_data['category']))
        if 'brand' in product_data:
            text_fields.append(str(product_data['brand']))
        if 'tags' in product_data:
            text_fields.append(str(product_data['tags']))
        
        return ' '.join(text_fields)
    
    def _create_product_vector(self, product_text: str) -> np.ndarray:
        """
        Create semantic embedding vector for product text using the embedding service.
        
        Args:
            product_text (str): Product text to vectorize
            
        Returns:
            np.ndarray: Semantic embedding vector
        """
        try:
            # Preprocess and validate text
            processed_text = self.embedding_service.preprocess_text(product_text)
            
            if not self.embedding_service.validate_text(processed_text):
                raise ValueError("Invalid text for embedding")
            
            # Create semantic embedding using the embedding service
            vector = self.embedding_service.create_embedding(processed_text)
            return vector
        except Exception as e:
            self.logger.error(f"Error creating vector: {str(e)}")
            raise
    
    def load_products_to_vector_db(self, dataset_path: str) -> bool:
        """
        Load products from cleaned dataset into Pinecone vector database.
        
        Args:
            dataset_path (str): Path to the cleaned dataset CSV file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Load the cleaned dataset
            df = pd.read_csv(dataset_path)
            self.logger.info(f"Loaded dataset with {len(df)} products")
            
            # Prepare vectors and metadata for all products
            vectors = []
            metadata_list = []
            
            for idx, row in df.iterrows():
                product_data = row.to_dict()
                
                # Prepare product text
                product_text = self._prepare_product_text(product_data)
                
                if not product_text.strip():
                    continue  # Skip products with no meaningful text
                
                # Create vector
                vector = self._create_product_vector(product_text)
                
                # Prepare metadata
                metadata = {
                    'product_id': str(idx),
                    'text': product_text[:1000],  # Limit text length for metadata
                }
                
                # Add original product data to metadata (excluding large fields)
                for key, value in product_data.items():
                    if isinstance(value, (str, int, float)) and len(str(value)) < 1000:
                        metadata[f'product_{key}'] = str(value)
                
                vectors.append(vector.tolist())
                metadata_list.append(metadata)
            
            # Upsert vectors using the improved method
            self.upsert_vectors(vectors, metadata_list)
            
            self.logger.info("Successfully loaded all products to vector database")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading products to vector database: {str(e)}")
            return False
    
    def upsert_vectors(self, vectors, metadata):
        """Insert or update vectors in the database.
        
        Args:
            vectors (list): List of vectors to insert/update
            metadata (list): List of metadata dictionaries for each vector
        """
        try:
            if len(vectors) == 0 or len(metadata) == 0:
                raise ValueError("Vectors and metadata cannot be empty")
                
            if len(vectors) != len(metadata):
                raise ValueError("Number of vectors must match number of metadata entries")
            
            # Create vector IDs if not provided
            ids = [f"prod_{i}" for i in range(len(vectors))]
            
            # Create vectors list in the format expected by Pinecone
            vectors_list = [
                (id, vector, meta)
                for id, vector, meta in zip(ids, vectors, metadata)
            ]
            
            # Upsert in batches of 100
            batch_size = 100
            for i in range(0, len(vectors_list), batch_size):
                batch = vectors_list[i:i + batch_size]
                self.index.upsert(vectors=batch)
                self.logger.info(f"Upserted batch of {len(batch)} vectors")
                
        except Exception as e:
            raise Exception(f"Failed to upsert vectors: {str(e)}")
    
    def search_similar_products(self, query: str, top_k: int = 5, filter_dict=None, score_threshold=None) -> List[Dict[str, Any]]:
        """
        Search for similar products using semantic similarity.
        
        Args:
            query (str): Search query
            top_k (int): Number of top results to return
            filter_dict (dict, optional): Metadata filters to apply
            score_threshold (float, optional): Minimum similarity score threshold
            
        Returns:
            List[Dict]: List of similar products with metadata
        """
        try:
            # Create query vector using semantic embedding
            query_vector = self._create_product_vector(query)
            
            # Search in Pinecone
            search_results = self.index.query(
                vector=query_vector.tolist(),
                top_k=top_k,
                include_metadata=True,
                filter=filter_dict
            )
            
            # Format results
            similar_products = []
            for match in search_results.matches:
                product_info = {
                    'id': match.id,
                    'score': match.score,
                    'metadata': match.metadata
                }
                similar_products.append(product_info)
            
            # Apply score threshold if provided
            if score_threshold is not None:
                similar_products = [
                    product for product in similar_products 
                    if product['score'] >= score_threshold
                ]
            
            return similar_products
            
        except Exception as e:
            self.logger.error(f"Error searching similar products: {str(e)}")
            return []
    
    def interpret_similarity_score(self, score: float) -> str:
        """
        Interpret the similarity score for cosine similarity.
        
        Args:
            score (float): The similarity score to interpret
            
        Returns:
            str: Human-readable interpretation of the score
        """
        if score > 0.9:
            return "Very Strong Match"
        elif score > 0.8:
            return "Strong Match"
        elif score > 0.7:
            return "Moderate Match"
        elif score > 0.5:
            return "Weak Match"
        else:
            return "Poor Match"
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the vector database index.
        
        Returns:
            Dict: Index statistics
        """
        try:
            stats = self.index.describe_index_stats()
            return {
                'total_vector_count': stats.total_vector_count,
                'dimension': stats.dimension,
                'index_fullness': stats.index_fullness,
                'namespaces': stats.namespaces
            }
        except Exception as e:
            self.logger.error(f"Error getting index stats: {str(e)}")
            return {}
    
    def get_similarity_analysis(self) -> Dict[str, Any]:
        """
        Get comprehensive analysis of similarity metrics used in the system.
        
        Returns:
            Dict: Similarity metrics analysis and justification
        """
        try:
            # Get metric characteristics
            characteristics = self.similarity_service.get_metric_characteristics()
            
            # Get justification for selected metric
            justification = self.similarity_service.justify_metric_selection()
            
            # Get embedding model info
            embedding_info = self.embedding_service.get_model_info()
            
            return {
                'selected_metric': 'cosine_similarity',
                'characteristics': characteristics,
                'justification': justification,
                'implementation_details': {
                    'pinecone_metric': 'cosine',
                    'embedding_model': embedding_info['model_name'],
                    'vector_dimension': embedding_info['dimension'],
                    'similarity_range': '0 to 1 (higher = more similar)'
                }
            }
        except Exception as e:
            self.logger.error(f"Error getting similarity analysis: {str(e)}")
            return {}
    
    def delete_index(self):
        """
        Delete the Pinecone index (use with caution).
        """
        try:
            self.pc.delete_index(self.index_name)
            self.logger.info(f"Deleted index: {self.index_name}")
        except Exception as e:
            self.logger.error(f"Error deleting index: {str(e)}")
            raise 