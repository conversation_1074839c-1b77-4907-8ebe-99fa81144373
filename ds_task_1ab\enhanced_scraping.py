#!/usr/bin/env python3
"""
Enhanced Web Scraping Script for Product Images
Based on the original script but with improved filtering and multi-product support.
Designed for Task 5: Web Scraping for Product Images for CNN training data.
"""

import pandas as pd
import requests
import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import StaleElementReferenceException, NoSuchElementException

class ProductImageScraper:
    """
    Enhanced product image scraper with better filtering and error handling.
    """
    
    def __init__(self, chromedriver_path=None, headless=True):
        """
        Initialize the scraper.
        
        Args:
            chromedriver_path: Path to chromedriver executable (optional if in PATH)
            headless: Whether to run browser in headless mode
        """
        self.chromedriver_path = chromedriver_path
        self.headless = headless
        self.driver = None
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def setup_driver(self):
        """Setup Chrome WebDriver with optimal settings."""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        try:
            if self.chromedriver_path:
                self.driver = webdriver.Chrome(executable_path=self.chromedriver_path, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
            
            # Execute script to remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✓ Chrome WebDriver initialized successfully")
            return True
            
        except Exception as e:
            print(f"✗ Failed to initialize WebDriver: {e}")
            return False
    
    def is_valid_product_image(self, img_url, alt_text=None):
        """
        Enhanced filtering to identify actual product images.
        
        Args:
            img_url: Image URL to check
            alt_text: Alt text of the image
            
        Returns:
            bool: True if it's likely a product image
        """
        if not img_url:
            return False
        
        # Convert to lowercase for case-insensitive checking
        url_lower = img_url.lower()
        alt_lower = alt_text.lower() if alt_text else ""
        
        # Exclude common non-product image indicators
        exclude_keywords = [
            'logo', 'icon', 'sprite', 'arrow', 'nav', 'banner', 'placeholder',
            'blank', 'transparent', 'favicon', 'cart', 'search', 'prime',
            'signin', 'avatar', 'profile', 'star', 'rating', 'badge', 'deal',
            'wishlist', 'heart', 'button', 'btn', 'loading', 'spinner',
            'pixel', '1x1', 'spacer', 'divider', 'ui-', 'flag-', 'thumb_'
        ]
        
        # Check URL and alt text for exclusion keywords
        if any(keyword in url_lower for keyword in exclude_keywords):
            return False
        
        if any(keyword in alt_lower for keyword in exclude_keywords):
            return False
        
        # Must have valid image extension
        valid_extensions = ['.jpg', '.jpeg', '.png', '.webp']
        if not any(ext in url_lower for ext in valid_extensions):
            return False
        
        # Check for minimum size (avoid tiny images)
        import re
        size_pattern = r'_(?:SL|AC_|UL|SR)(\d+)'
        size_matches = re.findall(size_pattern, img_url)
        if size_matches:
            max_size = max(int(size) for size in size_matches)
            if max_size < 150:  # Too small
                return False
        
        return True
    
    def download_image(self, url, save_path, timeout=10):
        """
        Download image from URL with error handling.
        
        Args:
            url: Image URL
            save_path: Local path to save the image
            timeout: Request timeout in seconds
            
        Returns:
            bool: True if download successful
        """
        try:
            response = self.session.get(url, timeout=timeout, stream=True)
            response.raise_for_status()
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✓ Downloaded: {os.path.basename(save_path)}")
            return True
            
        except Exception as e:
            print(f"✗ Failed to download {url}: {e}")
            return False
    
    def scrape_product_images(self, search_term, max_products=10, max_images_per_product=3, 
                            output_dir="data/scraped_images", start_stock_code=20000):
        """
        Scrape product images for a given search term.
        
        Args:
            search_term: Product to search for
            max_products: Maximum number of products to process
            max_images_per_product: Maximum images per product
            output_dir: Directory to save images
            start_stock_code: Starting stock code for naming
            
        Returns:
            list: List of dictionaries with product data
        """
        if not self.setup_driver():
            return []
        
        print(f"🔍 Searching for: {search_term}")
        print(f"📊 Target: {max_products} products, {max_images_per_product} images each")
        
        try:
            # Navigate to Amazon India
            url = "https://www.amazon.in/"
            self.driver.get(url)
            time.sleep(3)
            
            # Find and use search box
            search_box = self.driver.find_element(By.ID, "twotabsearchtextbox")
            search_box.clear()
            search_box.send_keys(search_term)
            
            # Click search button
            search_button = self.driver.find_element(By.ID, "nav-search-submit-button")
            search_button.click()
            time.sleep(3)
            
            # Find product containers
            product_containers = self.driver.find_elements(By.CSS_SELECTOR, "[data-component-type='s-search-result']")
            print(f"✓ Found {len(product_containers)} product containers")
            
            scraped_data = []
            stock_code = start_stock_code
            
            for i, container in enumerate(product_containers[:max_products]):
                try:
                    # Get product title
                    title_element = container.find_element(By.CSS_SELECTOR, "h2 a span")
                    product_name = title_element.text.strip()
                    
                    if not product_name:
                        continue
                    
                    # Find all images in this container
                    img_elements = container.find_elements(By.TAG_NAME, "img")
                    valid_images = []
                    
                    for img in img_elements:
                        img_url = img.get_attribute('src')
                        alt_text = img.get_attribute('alt') or ""
                        
                        if self.is_valid_product_image(img_url, alt_text):
                            valid_images.append(img_url)
                    
                    # Remove duplicates and limit count
                    unique_images = list(dict.fromkeys(valid_images))[:max_images_per_product]
                    
                    if not unique_images:
                        print(f"⚠️  No valid images found for: {product_name}")
                        continue
                    
                    # Create product directory
                    product_dir = os.path.join(output_dir, str(stock_code))
                    
                    # Download images
                    downloaded_count = 0
                    for idx, img_url in enumerate(unique_images):
                        filename = f"{stock_code}_{idx}.jpg"
                        save_path = os.path.join(product_dir, filename)
                        
                        if self.download_image(img_url, save_path):
                            relative_path = os.path.relpath(save_path, start=os.getcwd())
                            scraped_data.append({
                                'stockcode': str(stock_code),
                                'product_name': product_name,
                                'image': relative_path
                            })
                            downloaded_count += 1
                    
                    if downloaded_count > 0:
                        print(f"✓ [{i+1}/{max_products}] {product_name}: {downloaded_count} images")
                        stock_code += 1
                    
                    time.sleep(1)  # Be respectful
                    
                except Exception as e:
                    print(f"✗ Error processing product {i+1}: {e}")
                    continue
            
            return scraped_data
            
        except Exception as e:
            print(f"✗ Error during scraping: {e}")
            return []
        
        finally:
            if self.driver:
                self.driver.quit()
                print("✓ WebDriver closed")
    
    def scrape_multiple_products(self, product_list, products_per_category=5, 
                                images_per_product=3, output_dir="data/scraped_images"):
        """
        Scrape images for multiple product categories.
        
        Args:
            product_list: List of product search terms
            products_per_category: Number of products per category
            images_per_product: Number of images per product
            output_dir: Base output directory
            
        Returns:
            int: Total number of images downloaded
        """
        all_data = []
        total_images = 0
        start_stock_code = 20000
        
        print("🚀 MULTI-PRODUCT SCRAPING STARTED")
        print("=" * 60)
        
        for i, product in enumerate(product_list, 1):
            print(f"\n[{i}/{len(product_list)}] Processing: {product}")
            print("-" * 40)
            
            category_dir = os.path.join(output_dir, product.replace(" ", "_"))
            
            data = self.scrape_product_images(
                search_term=product,
                max_products=products_per_category,
                max_images_per_product=images_per_product,
                output_dir=category_dir,
                start_stock_code=start_stock_code
            )
            
            all_data.extend(data)
            total_images += len(data)
            start_stock_code += products_per_category
            
            print(f"✓ Category completed: {len(data)} images")
            
            # Delay between categories
            if i < len(product_list):
                print("⏳ Waiting 5 seconds...")
                time.sleep(5)
        
        # Save all data to CSV
        if all_data:
            df = pd.DataFrame(all_data)
            csv_path = "data/dataset/CNN_Model_Train_Data.csv"
            os.makedirs(os.path.dirname(csv_path), exist_ok=True)
            df.to_csv(csv_path, index=False)
            
            print("\n" + "=" * 60)
            print("📊 SCRAPING COMPLETED")
            print("=" * 60)
            print(f"Total categories: {len(product_list)}")
            print(f"Total images: {total_images}")
            print(f"CSV saved: {csv_path}")
            print("=" * 60)
        
        return total_images


def main():
    """Main execution function."""
    # Configuration
    CHROMEDRIVER_PATH = r'C:\Users\<USER>\Desktop\web driver\chromedriver.exe'  # Update this path
    
    # Product categories to scrape
    PRODUCT_CATEGORIES = [
        "laptop", "smartphone", "headphones", "camera", "tablet",
        "smartwatch", "wireless mouse", "keyboard", "monitor", "speaker"
    ]
    
    # Initialize scraper
    scraper = ProductImageScraper(chromedriver_path=CHROMEDRIVER_PATH, headless=False)
    
    # Choose scraping mode
    mode = input("Choose mode - (1) Single product or (2) Multiple products [1/2]: ").strip()
    
    if mode == "1":
        # Single product scraping
        product = input("Enter product to search for: ").strip() or "laptop"
        print(f"\n🔍 Scraping images for: {product}")
        
        data = scraper.scrape_product_images(
            search_term=product,
            max_products=10,
            max_images_per_product=3
        )
        
        if data:
            df = pd.DataFrame(data)
            csv_path = "data/dataset/CNN_Model_Train_Data.csv"
            os.makedirs(os.path.dirname(csv_path), exist_ok=True)
            df.to_csv(csv_path, index=False)
            print(f"\n✓ Saved {len(data)} records to {csv_path}")
    
    else:
        # Multiple products scraping
        print(f"\n🔍 Scraping images for {len(PRODUCT_CATEGORIES)} categories")
        total_images = scraper.scrape_multiple_products(
            product_list=PRODUCT_CATEGORIES,
            products_per_category=5,
            images_per_product=3
        )
        print(f"\n🎉 Total images downloaded: {total_images}")


if __name__ == "__main__":
    main()
