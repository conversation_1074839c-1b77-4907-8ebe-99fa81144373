import pytesseract
from PIL import Image
import cv2
import numpy as np
import logging
import os
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
import re

class OCRService:
    """
    Service for Optical Character Recognition (OCR) using Tesseract.
    Handles image preprocessing and text extraction from various image formats.
    """
    
    def __init__(self, tesseract_path: Optional[str] = None):
        """
        Initialize the OCR service.
        
        Args:
            tesseract_path (str, optional): Path to Tesseract executable
        """
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Configure Tesseract path if provided
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            self.logger.info(f"Tesseract path set to: {tesseract_path}")
        
        # Verify Tesseract installation
        try:
            version = pytesseract.get_tesseract_version()
            self.logger.info(f"Tesseract version: {version}")
        except Exception as e:
            self.logger.error(f"Tesseract not found or not properly configured: {str(e)}")
            raise Exception("Tesseract OCR is not properly installed. Please install Tesseract and ensure it's in your PATH.")
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image to improve OCR accuracy.
        
        Args:
            image (np.ndarray): Input image as numpy array
            
        Returns:
            np.ndarray: Preprocessed image
        """
        try:
            # Convert to grayscale if not already
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Apply noise reduction
            denoised = cv2.medianBlur(gray, 3)
            
            # Apply thresholding to get binary image
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Apply morphological operations to clean up the image
            kernel = np.ones((1, 1), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            self.logger.error(f"Error preprocessing image: {str(e)}")
            raise
    
    def extract_text(self, image_path: str, preprocess: bool = True) -> Dict[str, Any]:
        """
        Extract text from an image file.
        
        Args:
            image_path (str): Path to the image file
            preprocess (bool): Whether to apply image preprocessing
            
        Returns:
            Dict[str, Any]: Dictionary containing extracted text and metadata
        """
        try:
            # Validate file exists
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image file not found: {image_path}")
            
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            return self.extract_text_from_array(image, preprocess)
            
        except Exception as e:
            self.logger.error(f"Error extracting text from image {image_path}: {str(e)}")
            raise
    
    def extract_text_from_array(self, image: np.ndarray, preprocess: bool = True) -> Dict[str, Any]:
        """
        Extract text from a numpy array image.
        
        Args:
            image (np.ndarray): Input image as numpy array
            preprocess (bool): Whether to apply image preprocessing
            
        Returns:
            Dict[str, Any]: Dictionary containing extracted text and metadata
        """
        try:
            # Preprocess image if requested
            if preprocess:
                processed_image = self.preprocess_image(image)
            else:
                processed_image = image
            
            # Extract text using Tesseract
            text = pytesseract.image_to_string(processed_image)
            
            # Clean up the extracted text
            cleaned_text = self.clean_extracted_text(text)
            
            # Get confidence scores
            confidence_data = pytesseract.image_to_data(processed_image, output_type=pytesseract.Output.DICT)
            avg_confidence = np.mean([int(conf) for conf in confidence_data['conf'] if int(conf) > 0])
            
            return {
                'text': cleaned_text,
                'raw_text': text,
                'confidence': avg_confidence,
                'word_count': len(cleaned_text.split()),
                'preprocessed': preprocess
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting text from image array: {str(e)}")
            raise
    
    def extract_text_from_pil(self, image: Image.Image, preprocess: bool = True) -> Dict[str, Any]:
        """
        Extract text from a PIL Image object.
        
        Args:
            image (Image.Image): Input PIL image
            preprocess (bool): Whether to apply image preprocessing
            
        Returns:
            Dict[str, Any]: Dictionary containing extracted text and metadata
        """
        try:
            # Convert PIL image to numpy array
            image_array = np.array(image)
            
            # Convert RGB to BGR if needed (OpenCV uses BGR)
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                image_array = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return self.extract_text_from_array(image_array, preprocess)
            
        except Exception as e:
            self.logger.error(f"Error extracting text from PIL image: {str(e)}")
            raise
    
    def clean_extracted_text(self, text: str) -> str:
        """
        Clean and normalize extracted text.
        
        Args:
            text (str): Raw extracted text
            
        Returns:
            str: Cleaned text
        """
        try:
            # Remove extra whitespace and normalize
            cleaned = re.sub(r'\s+', ' ', text.strip())
            
            # Remove special characters that might be OCR artifacts
            cleaned = re.sub(r'[^\w\s\-.,!?]', '', cleaned)
            
            # Normalize line breaks
            cleaned = cleaned.replace('\n', ' ').replace('\r', ' ')
            
            # Remove multiple spaces
            cleaned = re.sub(r'\s+', ' ', cleaned)
            
            return cleaned.strip()
            
        except Exception as e:
            self.logger.error(f"Error cleaning text: {str(e)}")
            return text.strip()
    
    def validate_image(self, image_path: str) -> bool:
        """
        Validate if the image file is suitable for OCR.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            bool: True if image is valid for OCR
        """
        try:
            # Check file exists
            if not os.path.exists(image_path):
                return False
            
            # Check file size (not too small, not too large)
            file_size = os.path.getsize(image_path)
            if file_size < 1024 or file_size > 10 * 1024 * 1024:  # 1KB to 10MB
                return False
            
            # Try to load image
            image = cv2.imread(image_path)
            if image is None:
                return False
            
            # Check image dimensions
            height, width = image.shape[:2]
            if height < 50 or width < 50:  # Minimum size
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating image {image_path}: {str(e)}")
            return False
    
    def get_supported_formats(self) -> list:
        """
        Get list of supported image formats.
        
        Returns:
            list: List of supported file extensions
        """
        return ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif']
    
    def is_supported_format(self, file_path: str) -> bool:
        """
        Check if the file format is supported for OCR.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if format is supported
        """
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.get_supported_formats() 