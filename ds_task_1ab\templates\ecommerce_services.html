<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Recommendation System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            background: #fff;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .results-section {
            margin-top: 30px;
        }
        .natural-language-response {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .error {
            background: #ffe6e6;
            color: #d32f2f;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border-left: 4px solid #d32f2f;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .score {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Product Recommendation System</h1>
        
        <div class="form-section">
            <h2>Search Products</h2>
            <form id="recommendationForm">
                <div class="form-group">
                    <label for="query">Enter your query:</label>
                    <input type="text" id="query" name="query" placeholder="e.g., gaming laptop, wireless headphones, camera for beginners" required>
                </div>
                <button type="submit">Get Recommendations</button>
            </form>
        </div>
        
        <div id="results" class="results-section" style="display: none;">
            <div id="loading" class="loading" style="display: none;">
                Searching for products...
            </div>
            
            <div id="error" class="error" style="display: none;"></div>
            
            <div id="naturalLanguageResponse" class="natural-language-response" style="display: none;"></div>
            
            <div id="productsTable" style="display: none;">
                <h2>Recommended Products</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Stock Code</th>
                            <th>Description</th>
                            <th>Unit Price (£)</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('recommendationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const query = document.getElementById('query').value;
            const resultsDiv = document.getElementById('results');
            const loadingDiv = document.getElementById('loading');
            const errorDiv = document.getElementById('error');
            const naturalLanguageDiv = document.getElementById('naturalLanguageResponse');
            const productsTableDiv = document.getElementById('productsTable');
            
            // Show results section and loading
            resultsDiv.style.display = 'block';
            loadingDiv.style.display = 'block';
            errorDiv.style.display = 'none';
            naturalLanguageDiv.style.display = 'none';
            productsTableDiv.style.display = 'none';
            
            try {
                const formData = new FormData();
                formData.append('query', query);
                
                const response = await fetch('/product-recommendation', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                // Hide loading
                loadingDiv.style.display = 'none';
                
                if (result.error) {
                    // Show error
                    errorDiv.textContent = result.error;
                    errorDiv.style.display = 'block';
                } else {
                    // Show natural language response
                    naturalLanguageDiv.textContent = result.response;
                    naturalLanguageDiv.style.display = 'block';
                    
                    // Show products table if there are products
                    if (result.products && result.products.length > 0) {
                        const tbody = document.getElementById('productsTableBody');
                        tbody.innerHTML = '';
                        
                        result.products.forEach(product => {
                            const row = document.createElement('tr');
                            
                            row.innerHTML = `
                                <td>${product.stock_code}</td>
                                <td>${product.description}</td>
                                <td>£${parseFloat(product.unit_price).toFixed(2)}</td>
                            `;
                            
                            tbody.appendChild(row);
                        });
                        
                        productsTableDiv.style.display = 'block';
                    }
                }
                
            } catch (error) {
                loadingDiv.style.display = 'none';
                errorDiv.textContent = 'An error occurred while processing your request. Please try again.';
                errorDiv.style.display = 'block';
                console.error('Error:', error);
            }
        });
    </script>
</body>
</html> 