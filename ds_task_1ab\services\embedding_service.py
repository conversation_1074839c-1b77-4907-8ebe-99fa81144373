import numpy as np
from typing import List, Dict, Any
from sentence_transformers import SentenceTransformer
import logging

class EmbeddingService:
    """
    Dedicated service for handling vector embeddings using modern embedding models.
    Separates embedding logic from vector database operations.
    """
    
    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        """
        Initialize the embedding service.
        
        Args:
            model_name (str): Name of the sentence transformer model to use
        """
        self.model_name = model_name
        self.embedding_model = SentenceTransformer(model_name)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"Initialized EmbeddingService with model: {model_name}")
    
    def create_embedding(self, text: str) -> np.ndarray:
        """
        Create semantic embedding vector for text using the configured model.
        
        Args:
            text (str): Text to vectorize
            
        Returns:
            np.ndarray: Semantic embedding vector
        """
        try:
            # Create semantic embedding
            vector = self.embedding_model.encode(text)
            return vector
        except Exception as e:
            self.logger.error(f"Error creating embedding: {str(e)}")
            raise
    
    def get_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """
        Create embeddings for multiple texts.
        
        Args:
            texts (List[str]): List of texts to vectorize
            
        Returns:
            List[np.ndarray]: List of embedding vectors
        """
        try:
            # Create embeddings in batch
            vectors = self.embedding_model.encode(texts)
            return [vectors[i] for i in range(len(vectors))]
        except Exception as e:
            self.logger.error(f"Error creating embeddings: {str(e)}")
            raise
    
    def create_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """
        Create embeddings for multiple texts in batch for efficiency.
        
        Args:
            texts (List[str]): List of texts to vectorize
            
        Returns:
            np.ndarray: Array of embedding vectors
        """
        try:
            # Create embeddings in batch
            vectors = self.embedding_model.encode(texts)
            return vectors
        except Exception as e:
            self.logger.error(f"Error creating batch embeddings: {str(e)}")
            raise
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        
        Returns:
            int: Dimension of the embedding vectors
        """
        # Create a dummy embedding to get the dimension
        dummy_vector = self.create_embedding("test")
        return len(dummy_vector)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the embedding model.
        
        Returns:
            Dict: Model information
        """
        return {
            'model_name': self.model_name,
            'dimension': self.get_embedding_dimension(),
            'max_sequence_length': self.embedding_model.max_seq_length,
            'model_type': 'sentence_transformer'
        }
    
    def validate_text(self, text: str) -> bool:
        """
        Validate if text is suitable for embedding.
        
        Args:
            text (str): Text to validate
            
        Returns:
            bool: True if text is valid for embedding
        """
        if not text or not text.strip():
            return False
        
        # Check if text is too long (most models have limits)
        if len(text) > self.embedding_model.max_seq_length:
            self.logger.warning(f"Text too long for model: {len(text)} > {self.embedding_model.max_seq_length}")
            return False
        
        return True
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text before embedding.
        
        Args:
            text (str): Raw text
            
        Returns:
            str: Preprocessed text
        """
        # Basic preprocessing
        text = text.strip()
        
        # Truncate if too long
        if len(text) > self.embedding_model.max_seq_length:
            text = text[:self.embedding_model.max_seq_length]
        
        return text 