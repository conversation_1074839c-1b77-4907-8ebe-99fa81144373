#!/usr/bin/env python3
"""
Automated Web Scraping for CNN Training Data

This script automates the process of:
1. Generating stock codes starting from 20000
2. Scraping product images from multiple sources
3. Downloading and storing images systematically
4. Updating CNN_Model_Train_Data.csv with stock codes and image paths

The CSV file will have the structure:
stockcode,image
20000,data/scraped_images/20000/20000_0.jpg
20001,data/scraped_images/20001/20001_0.jpg
...

Note: The 'image' column contains relative file paths that the CNN model can use to load images.
"""

import os
import sys
import time
import logging
from services.web_scraping_service import WebScrapingService

def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('scraping.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main function to run automated scraping."""
    logger = setup_logging()
    
    print("=" * 60)
    print("🚀 AUTOMATED WEB SCRAPING FOR CNN TRAINING DATA")
    print("=" * 60)
    print()
    
    try:
        # Initialize web scraping service
        logger.info("Initializing WebScrapingService...")
        scraper = WebScrapingService(output_dir="data/scraped_images")
        
        # Configuration
        TOTAL_PRODUCTS = 20000
        START_FROM = 20000
        MAX_IMAGES_PER_PRODUCT = 1
        
        print(f"📋 Configuration:")
        print(f"   • Total products to scrape: {TOTAL_PRODUCTS}")
        print(f"   • Stock codes starting from: {START_FROM}")
        print(f"   • Images per product: {MAX_IMAGES_PER_PRODUCT}")
        print(f"   • Output directory: data/scraped_images")
        print(f"   • CSV file: data/dataset/CNN_Model_Train_Data.csv")
        print()
        
        # Generate stock codes
        logger.info(f"Generating {TOTAL_PRODUCTS} stock codes starting from {START_FROM}...")
        stock_codes = scraper.generate_stock_codes(TOTAL_PRODUCTS, START_FROM)
        
        print(f"✅ Generated {len(stock_codes)} stock codes")
        print(f"   • First stock code: {stock_codes[0]}")
        print(f"   • Last stock code: {stock_codes[-1]}")
        print()
        
        # Start scraping process
        print("🔄 Starting automated scraping process...")
        print("   This may take several hours depending on the number of products.")
        print("   Progress will be logged to 'scraping.log'")
        print()
        
        start_time = time.time()
        
        # Run scraping
        results = scraper.scrape_product_images(
            stock_codes=stock_codes,
            max_per_product=MAX_IMAGES_PER_PRODUCT
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Print results
        print("=" * 60)
        print("📊 SCRAPING RESULTS")
        print("=" * 60)
        print(f"⏱️  Total time: {duration:.2f} seconds ({duration/3600:.2f} hours)")
        print(f"📦 Total products processed: {results['total_products']}")
        print(f"✅ Successful products: {results['successful_products']}")
        print(f"❌ Failed products: {results['failed_products']}")
        print(f"🖼️  Total images downloaded: {results['total_images_downloaded']}")
        print(f"📈 Success rate: {(results['successful_products']/results['total_products']*100):.1f}%")
        print()
        
        # Get file statistics
        stats = scraper.get_scraping_stats()
        print("📁 FILE STATISTICS")
        print(f"   • Output directory: {stats['output_directory']}")
        print(f"   • Total files: {stats['total_files']}")
        print(f"   • Total directories: {stats['total_directories']}")
        print()
        
        # Verify CSV file
        csv_path = "data/dataset/CNN_Model_Train_Data.csv"
        if os.path.exists(csv_path):
            import pandas as pd
            df = pd.read_csv(csv_path)
            print("📄 CSV FILE VERIFICATION")
            print(f"   • File path: {csv_path}")
            print(f"   • Total records: {len(df)}")
            print(f"   • Columns: {list(df.columns)}")
            print(f"   • Records with images: {len(df[df['image'] != ''])}")
            print(f"   • Records without images: {len(df[df['image'] == ''])}")
            print()
            
            # Show sample data
            print("📋 SAMPLE DATA (first 5 records):")
            print(df.head().to_string(index=False))
            print()
        
        print("✅ AUTOMATED SCRAPING COMPLETED SUCCESSFULLY!")
        print()
        print("🎯 Next steps:")
        print("   1. Review the scraped images in data/scraped_images/")
        print("   2. Check CNN_Model_Train_Data.csv for data quality")
        print("   3. Proceed with CNN model training using the collected data")
        print()
        
    except KeyboardInterrupt:
        print("\n⚠️  Scraping interrupted by user")
        logger.warning("Scraping process interrupted by user")
        
    except Exception as e:
        print(f"\n❌ Error during scraping: {str(e)}")
        logger.error(f"Scraping failed: {str(e)}")
        sys.exit(1)
        
    finally:
        # Clean up resources
        if 'scraper' in locals():
            logger.info("Cleaning up resources...")
            scraper.cleanup()
            print("🧹 Resources cleaned up")

if __name__ == "__main__":
    main() 