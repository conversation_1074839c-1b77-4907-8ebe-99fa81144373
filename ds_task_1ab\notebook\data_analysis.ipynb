{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# E-commerce Dataset Analysis\n", "\n", "This notebook analyzes the e-commerce dataset to understand its structure and identify cleaning needs.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('display.float_format', lambda x: '%.2f' % x)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:2: <PERSON>yntaxWarning: invalid escape sequence '\\d'\n", "<>:2: <PERSON>yntaxWarning: invalid escape sequence '\\d'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10216\\*********.py:2: SyntaxWarning: invalid escape sequence '\\d'\n", "  df = pd.read_csv('data\\dataset\\dataset.csv')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset Shape:\n", "(541909, 8)\n", "\n", "Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 541909 entries, 0 to 541908\n", "Data columns (total 8 columns):\n", " #   Column       Non-Null Count   Dtype \n", "---  ------       --------------   ----- \n", " 0   InvoiceNo    541909 non-null  object\n", " 1   StockCode    541909 non-null  object\n", " 2   Description  540884 non-null  object\n", " 3   Quantity     541909 non-null  object\n", " 4   InvoiceDate  541909 non-null  object\n", " 5   UnitPrice    541909 non-null  object\n", " 6   CustomerID   433909 non-null  object\n", " 7   Country      541909 non-null  object\n", "dtypes: object(8)\n", "memory usage: 33.1+ MB\n", "\n", "Memory Usage:\n", "262.81177520751953 MB\n"]}], "source": ["# Read the dataset\n", "df = pd.read_csv('data\\dataset\\dataset.csv')\n", "\n", "# Basic information about the dataset\n", "print(\"\\nDataset Shape:\")\n", "print(df.shape)\n", "\n", "print(\"\\nDataset Info:\")\n", "df.info()\n", "\n", "print(\"\\nMemory Usage:\")\n", "print(df.memory_usage(deep=True).sum() / 1024**2, 'MB')\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Missing Values Analysis:\n", "             Missing Values  Percentage\n", "CustomerID           108000       19.93\n", "Description            1025        0.19\n"]}], "source": ["# Check for missing values\n", "missing_values = df.isnull().sum()\n", "missing_percentages = (missing_values / len(df)) * 100\n", "\n", "print(\"\\nMissing Values Analysis:\")\n", "missing_df = pd.DataFrame({\n", "    'Missing Values': missing_values,\n", "    'Percentage': missing_percentages\n", "}).sort_values('Missing Values', ascending=False)\n", "\n", "print(missing_df[missing_df['Missing Values'] > 0])\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Duplicate Rows:\n", "Number of duplicate rows: 91\n", "\n", "Duplicate Values by Column:\n", "InvoiceNo: 497183 duplicates\n", "StockCode: 534104 duplicates\n", "Description: 533952 duplicates\n", "Quantity: 540850 duplicates\n", "InvoiceDate: 518649 duplicates\n", "UnitPrice: 539894 duplicates\n", "CustomerID: 533441 duplicates\n", "Country: 541833 duplicates\n"]}], "source": ["# Check for duplicates\n", "print(\"\\nDuplicate Rows:\")\n", "print(f\"Number of duplicate rows: {df.duplicated().sum()}\")\n", "\n", "# For each column\n", "print(\"\\nDuplicate Values by Column:\")\n", "for column in df.columns:\n", "    duplicates = df[column].duplicated().sum()\n", "    if duplicates > 0:\n", "        print(f\"{column}: {duplicates} duplicates\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Numerical Columns Statistics:\n", "       InvoiceNo StockCode                         Description Quantity  \\\n", "count     541909    541909                              540884   541909   \n", "unique     44726      7805                                7956     1059   \n", "top      573585ä    85123A  WHITE HANGING HEART T-LIGHT HOLDER        1   \n", "freq         561      1184                                1699    88742   \n", "\n", "                InvoiceDate UnitPrice CustomerID         Country  \n", "count                541909    541909     433909          541909  \n", "unique                23260      2015       8467              76  \n", "top     2011-10-31 14:41:00      1.25      &nan#  United Kingdom  \n", "freq                   1114     40287      27080          248303  \n"]}], "source": ["# Basic statistics for numerical columns\n", "print(\"\\nNumerical Columns Statistics:\")\n", "print(df.describe())\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Categorical Columns Analysis:\n", "\n", "InvoiceNo:\n", "Number of unique values: 44726\n", "\n", "Top 10 most common values:\n", "InvoiceNo\n", "573585ä    561\n", "573585     553\n", "580729ä    386\n", "581219ä    376\n", "581219     373\n", "579777ä    370\n", "581492     368\n", "558475ä    366\n", "581492ä    363\n", "537434     348\n", "Name: count, dtype: int64\n", "\n", "StockCode:\n", "Number of unique values: 7805\n", "\n", "Top 10 most common values:\n", "StockCode\n", "85123A      1184\n", "ö85123A^    1129\n", "ö22423^     1114\n", "22423       1089\n", "ö85099B^    1080\n", "85099B      1079\n", "ö47566^      873\n", "47566        854\n", "20725        821\n", "ö20725^      818\n", "Name: count, dtype: int64\n", "\n", "Description:\n", "Number of unique values: 7956\n", "\n", "Top 10 most common values:\n", "Description\n", "WHITE HANGING HEART T-LIGHT HOLDER    1699\n", "REGENCY CAKESTAND 3 TIER              1537\n", "JUMBO BAG RED RETROSPOT               1535\n", "PARTY BUNTING                         1227\n", "LUNCH BAG RED RETROSPOT               1111\n", "ASSORTED COLOUR BIRD ORNAMENT         1056\n", "SET OF 3 CAKE TINS PANTRY DESIGN      1025\n", "PACK OF 72 R<PERSON><PERSON><PERSON><PERSON> CAKE CASES        973\n", "LUNCH BAG  BLACK SKULL.                948\n", "HEART OF WICKER SMALL                  900\n", "Name: count, dtype: int64\n", "\n", "Quantity:\n", "Number of unique values: 1059\n", "\n", "Top 10 most common values:\n", "Quantity\n", "1      88742\n", "1@     59485\n", "2      49105\n", "12     36703\n", "2@     32724\n", "6      24614\n", "12@    24360\n", "4      23205\n", "3      22283\n", "6@     16254\n", "Name: count, dtype: int64\n", "\n", "InvoiceDate:\n", "Number of unique values: 23260\n", "\n", "Top 10 most common values:\n", "InvoiceDate\n", "2011-10-31 14:41:00    1114\n", "2011-12-08 09:28:00     749\n", "2011-12-09 10:03:00     731\n", "2011-12-05 17:24:00     721\n", "2011-06-29 15:58:00     705\n", "2011-11-30 15:13:00     687\n", "2011-12-08 09:20:00     676\n", "2010-12-06 16:57:00     675\n", "2011-12-05 17:28:00     662\n", "2010-12-09 14:09:00     652\n", "Name: count, dtype: int64\n", "\n", "UnitPrice:\n", "Number of unique values: 2015\n", "\n", "Top 10 most common values:\n", "UnitPrice\n", "1.25    40287\n", "1.65    30525\n", "0.85    22757\n", "2.95    22148\n", "0.42    19631\n", "4.95    15247\n", "3.75    14844\n", "2.1     14203\n", "2.46    13712\n", "2.08    13638\n", "Name: count, dtype: int64\n", "\n", "CustomerID:\n", "Number of unique values: 8467\n", "\n", "Top 10 most common values:\n", "CustomerID\n", "&nan#        27080\n", "17841.0       6421\n", "14911.0       4747\n", "14096.0       4118\n", "12748.0       3727\n", "14606.0       2234\n", "15311.0       2018\n", "14646.0       1641\n", "&17841.0#     1562\n", "13089.0       1479\n", "Name: count, dtype: int64\n", "\n", "Country:\n", "Number of unique values: 76\n", "\n", "Top 10 most common values:\n", "Country\n", "United Kingdom         248303\n", "XxYUnited Kingdom☺️    247175\n", "XxYGermany☺️             4794\n", "Germany                  4701\n", "France                   4330\n", "XxYFrance☺️              4227\n", "EIRE                     4105\n", "XxYEIRE☺️                4091\n", "Spain                    1268\n", "XxYSpain☺️               1265\n", "Name: count, dtype: int64\n"]}], "source": ["# Analyze categorical columns\n", "categorical_columns = df.select_dtypes(include=['object']).columns\n", "\n", "print(\"\\nCategorical Columns Analysis:\")\n", "for column in categorical_columns:\n", "    print(f\"\\n{column}:\")\n", "    print(f\"Number of unique values: {df[column].nunique()}\")\n", "    print(\"\\nTop 10 most common values:\")\n", "    print(df[column].value_counts().head(10))\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Format Consistency Analysis:\n", "\n", "InvoiceNo:\n", "- Contains mixed case values\n", "\n", "StockCode:\n", "- Contains mixed case values\n", "\n", "Description:\n", "- 113185 values have leading/trailing spaces\n", "- Contains mixed case values\n", "\n", "Quantity:\n", "- Contains mixed case values\n", "\n", "InvoiceDate:\n", "- Contains mixed case values\n", "\n", "UnitPrice:\n", "- Contains mixed case values\n", "\n", "CustomerID:\n", "- Contains mixed case values\n", "\n", "Country:\n", "- Contains mixed case values\n"]}], "source": ["# Check for potential format inconsistencies in string columns\n", "print(\"\\nFormat Consistency Analysis:\")\n", "for column in categorical_columns:\n", "    print(f\"\\n{column}:\")\n", "    # Check for leading/trailing spaces\n", "    spaces = df[column].astype(str).str.strip() != df[column].astype(str)\n", "    if spaces.any():\n", "        print(f\"- {spaces.sum()} values have leading/trailing spaces\")\n", "    \n", "    # Check for mixed case\n", "    if df[column].dtype == 'object':\n", "        lower = df[column].astype(str).str.islower()\n", "        upper = df[column].astype(str).str.isupper()\n", "        if not (all(lower) or all(upper)):\n", "            print(\"- Contains mixed case values\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Duplicate Rows Removed:\n", "Original number of rows: 541909\n", "Rows after removing duplicates: 541818\n", "Number of duplicate rows removed: 91\n"]}], "source": ["# Remove duplicates and show the number of rows removed\n", "original_rows = len(df)\n", "df = df.drop_duplicates()\n", "removed_rows = original_rows - len(df)\n", "\n", "print(f\"\\nDuplicate Rows Removed:\")\n", "print(f\"Original number of rows: {original_rows}\")\n", "print(f\"Rows after removing duplicates: {len(df)}\")\n", "print(f\"Number of duplicate rows removed: {removed_rows}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Missing Values Analysis:\n", "\n", "Count of missing values:\n", "Description      1025\n", "CustomerID     108000\n", "dtype: int64\n", "\n", "Percentage of missing values:\n", "Description    0.19\n", "CustomerID    19.93\n", "dtype: float64\n", "\n", "Dropped rows with missing Description values\n", "\n", "Dropped CustomerID column\n", "\n", "All missing values have been handled successfully\n"]}], "source": ["# Analyze missing values\n", "missing_values = df.isnull().sum()\n", "missing_percentages = (missing_values / len(df)) * 100\n", "\n", "print(\"\\nMissing Values Analysis:\")\n", "print(\"\\nCount of missing values:\")\n", "print(missing_values[missing_values > 0])\n", "print(\"\\nPercentage of missing values:\")\n", "print(missing_percentages[missing_percentages > 0])\n", "\n", "# Drop rows with missing Description values\n", "df = df.dropna(subset=['Description'])\n", "print(\"\\nDropped rows with missing Description values\")\n", "\n", "\n", "# Drop CustomerID column\n", "df = df.drop('CustomerID', axis=1)\n", "print(\"\\nDropped CustomerID column\")\n", "\n", "# Verify remaining missing values\n", "remaining_missing = df.isnull().sum()\n", "if remaining_missing.sum() == 0:\n", "    print(\"\\nAll missing values have been handled successfully\")\n", "else:\n", "    print(\"\\nRemaining missing values:\")\n", "    print(remaining_missing[remaining_missing > 0])\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InvoiceNo</th>\n", "      <th>StockCode</th>\n", "      <th>Description</th>\n", "      <th>Quantity</th>\n", "      <th>InvoiceDate</th>\n", "      <th>UnitPrice</th>\n", "      <th>Country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>536365</td>\n", "      <td>85123A</td>\n", "      <td>WHITE HANGING HEART T-LIGHT HOLDER</td>\n", "      <td>6</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.55</td>\n", "      <td>XxYUnited Kingdom☺️</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>536365</td>\n", "      <td>71053</td>\n", "      <td>WHITE METAL LANTERN</td>\n", "      <td>6</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>536365</td>\n", "      <td>ö84406B^</td>\n", "      <td>CREAM CUPID HEARTS COAT HANGER</td>\n", "      <td>8</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.75</td>\n", "      <td>XxYUnited Kingdom☺️</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>536365</td>\n", "      <td>84029G</td>\n", "      <td>$KNITTED UNION FLAG HOT WATER BOTTLE</td>\n", "      <td>6@</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>536365</td>\n", "      <td>84029E</td>\n", "      <td>$RED WOOLLY HOTTIE WHITE HEART.</td>\n", "      <td>6@</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541904</th>\n", "      <td>581587ä</td>\n", "      <td>ö22613^</td>\n", "      <td>PACK OF 20 SPACEBOY NAPKINS</td>\n", "      <td>12@</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>Ww0.85</td>\n", "      <td>XxYFrance☺️</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541905</th>\n", "      <td>581587</td>\n", "      <td>ö22899^</td>\n", "      <td>CHILDREN'S APRON DOLLY GIRL</td>\n", "      <td>6</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>2.1</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541906</th>\n", "      <td>581587</td>\n", "      <td>23254</td>\n", "      <td>CHILDRENS CUTLERY DOLLY GIRL</td>\n", "      <td>4</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>XxYFrance☺️</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541907</th>\n", "      <td>581587ä</td>\n", "      <td>23255</td>\n", "      <td>CH<PERSON><PERSON>ENS CUTLERY CIRCUS PARADE</td>\n", "      <td>4</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>XxYFrance☺️</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541908</th>\n", "      <td>581587</td>\n", "      <td>22138</td>\n", "      <td>BAKING SET 9 PIECE RETROSPOT</td>\n", "      <td>3@</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.95</td>\n", "      <td>France</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>540793 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       InvoiceNo StockCode                           Description Quantity  \\\n", "0         536365    85123A    WHITE HANGING HEART T-LIGHT HOLDER        6   \n", "1         536365     71053                   WHITE METAL LANTERN        6   \n", "2         536365  ö84406B^        CREAM CUPID HEARTS COAT HANGER        8   \n", "3         536365    84029G  $KNITTED UNION FLAG HOT WATER BOTTLE       6@   \n", "4         536365    84029E       $RED WOOLLY HOTTIE WHITE HEART.       6@   \n", "...          ...       ...                                   ...      ...   \n", "541904   581587ä   ö22613^           PACK OF 20 SPACEBOY NAPKINS      12@   \n", "541905    581587   ö22899^          CHILDREN'S APRON DOLLY GIRL         6   \n", "541906    581587     23254         CHILDRENS CUTLERY DOLLY GIRL         4   \n", "541907   581587ä     23255       CHILDRENS CUTLERY CIRCUS PARADE        4   \n", "541908    581587     22138         BAKING SET 9 PIECE RETROSPOT        3@   \n", "\n", "                InvoiceDate UnitPrice              Country  \n", "0       2010-12-01 08:26:00      2.55  XxYUnited Kingdom☺️  \n", "1       2010-12-01 08:26:00      3.39       United Kingdom  \n", "2       2010-12-01 08:26:00      2.75  XxYUnited Kingdom☺️  \n", "3       2010-12-01 08:26:00      3.39       United Kingdom  \n", "4       2010-12-01 08:26:00      3.39       United Kingdom  \n", "...                     ...       ...                  ...  \n", "541904  2011-12-09 12:50:00    Ww0.85          XxYFrance☺️  \n", "541905  2011-12-09 12:50:00       2.1               France  \n", "541906  2011-12-09 12:50:00      4.15          XxYFrance☺️  \n", "541907  2011-12-09 12:50:00      4.15          XxYFrance☺️  \n", "541908  2011-12-09 12:50:00      4.95               France  \n", "\n", "[540793 rows x 7 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning special characters and unnecessary symbols from columns...\n", "\n", "Cleaning completed. Sample of cleaned data:\n", "  InvoiceNo StockCode                          Description  Quantity  \\\n", "0    536365    85123A   WHITE HANGING HEART T-LIGHT HOLDER      6.00   \n", "1    536365     71053                  WHITE METAL LANTERN      6.00   \n", "2    536365   ö84406B       CREAM CUPID HEARTS COAT HANGER      8.00   \n", "3    536365    84029G  KNITTED UNION FLAG HOT WATER BOTTLE      6.00   \n", "4    536365    84029E       RED WOOLLY HOTTIE WHITE HEART.      6.00   \n", "\n", "           InvoiceDate  UnitPrice            Country  \n", "0  2010-12-01 08:26:00       2.55  XxYUnited Kingdom  \n", "1  2010-12-01 08:26:00       3.39     United Kingdom  \n", "2  2010-12-01 08:26:00       2.75  XxYUnited Kingdom  \n", "3  2010-12-01 08:26:00       3.39     United Kingdom  \n", "4  2010-12-01 08:26:00       3.39     United Kingdom  \n"]}], "source": ["# Clean special characters and unnecessary symbols from all columns\n", "print(\"Cleaning special characters and unnecessary symbols from columns...\")\n", "\n", "# Function to clean text data\n", "def clean_text(text):\n", "    if isinstance(text, str):\n", "        # Remove special characters, emojis, and unnecessary symbols\n", "        cleaned = ''.join(char for char in text if char.isalnum() or char.isspace() or char in '.,()-/')\n", "        # Remove leading/trailing whitespace and $ signs\n", "        cleaned = cleaned.strip().strip('$')\n", "        # Replace multiple spaces with single space\n", "        cleaned = ' '.join(cleaned.split())\n", "        return cleaned\n", "    return text\n", "\n", "# Clean numeric columns separately\n", "def clean_numeric(value):\n", "    if isinstance(value, str):\n", "        # Remove any non-numeric characters except decimal point\n", "        cleaned = ''.join(char for char in value if char.isdigit() or char == '.')\n", "        # Convert to float if possible\n", "        try:\n", "            return float(cleaned)\n", "        except:\n", "            return value\n", "    return value\n", "\n", "# Apply cleaning to each column based on its type\n", "df['Description'] = df['Description'].apply(clean_text)\n", "df['Country'] = df['Country'].apply(clean_text)\n", "df['StockCode'] = df['StockCode'].apply(clean_text)\n", "df['InvoiceNo'] = df['InvoiceNo'].apply(clean_text)\n", "df['Quantity'] = df['Quantity'].apply(clean_numeric)\n", "df['UnitPrice'] = df['UnitPrice'].apply(clean_numeric)\n", "\n", "print(\"\\nCleaning completed. Sample of cleaned data:\")\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InvoiceNo</th>\n", "      <th>StockCode</th>\n", "      <th>Description</th>\n", "      <th>Quantity</th>\n", "      <th>InvoiceDate</th>\n", "      <th>UnitPrice</th>\n", "      <th>Country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>536365</td>\n", "      <td>85123A</td>\n", "      <td>WHITE HANGING HEART T-LIGHT HOLDER</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.55</td>\n", "      <td>XxYUnited Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>536365</td>\n", "      <td>71053</td>\n", "      <td>WHITE METAL LANTERN</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>536365</td>\n", "      <td>ö84406B</td>\n", "      <td>CREAM CUPID HEARTS COAT HANGER</td>\n", "      <td>8.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.75</td>\n", "      <td>XxYUnited Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>536365</td>\n", "      <td>84029G</td>\n", "      <td>KNITTED UNION FLAG HOT WATER BOTTLE</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>536365</td>\n", "      <td>84029E</td>\n", "      <td>RED WOOLLY HOTTIE WHITE HEART.</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541904</th>\n", "      <td>581587ä</td>\n", "      <td>ö22613</td>\n", "      <td>PACK OF 20 SPACEBOY NAPKINS</td>\n", "      <td>12.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>0.85</td>\n", "      <td>XxYFrance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541905</th>\n", "      <td>581587</td>\n", "      <td>ö22899</td>\n", "      <td>CHILDRENS APRON DOLLY GIRL</td>\n", "      <td>6.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>2.10</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541906</th>\n", "      <td>581587</td>\n", "      <td>23254</td>\n", "      <td>CHILDRENS CUTLERY DOLLY GIRL</td>\n", "      <td>4.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>XxYFrance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541907</th>\n", "      <td>581587ä</td>\n", "      <td>23255</td>\n", "      <td>CH<PERSON><PERSON>ENS CUTLERY CIRCUS PARADE</td>\n", "      <td>4.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>XxYFrance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541908</th>\n", "      <td>581587</td>\n", "      <td>22138</td>\n", "      <td>BAKING SET 9 PIECE RETROSPOT</td>\n", "      <td>3.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.95</td>\n", "      <td>France</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>540793 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       InvoiceNo StockCode                          Description  Quantity  \\\n", "0         536365    85123A   WHITE HANGING HEART T-LIGHT HOLDER      6.00   \n", "1         536365     71053                  WHITE METAL LANTERN      6.00   \n", "2         536365   ö84406B       CREAM CUPID HEARTS COAT HANGER      8.00   \n", "3         536365    84029G  KNITTED UNION FLAG HOT WATER BOTTLE      6.00   \n", "4         536365    84029E       RED WOOLLY HOTTIE WHITE HEART.      6.00   \n", "...          ...       ...                                  ...       ...   \n", "541904   581587ä    ö22613          PACK OF 20 SPACEBOY NAPKINS     12.00   \n", "541905    581587    ö22899           CHILDRENS APRON DOLLY GIRL      6.00   \n", "541906    581587     23254         CHILDRENS CUTLERY DOLLY GIRL      4.00   \n", "541907   581587ä     23255      CHILDRENS CUTLERY CIRCUS PARADE      4.00   \n", "541908    581587     22138         BAKING SET 9 PIECE RETROSPOT      3.00   \n", "\n", "                InvoiceDate  UnitPrice            Country  \n", "0       2010-12-01 08:26:00       2.55  XxYUnited Kingdom  \n", "1       2010-12-01 08:26:00       3.39     United Kingdom  \n", "2       2010-12-01 08:26:00       2.75  XxYUnited Kingdom  \n", "3       2010-12-01 08:26:00       3.39     United Kingdom  \n", "4       2010-12-01 08:26:00       3.39     United Kingdom  \n", "...                     ...        ...                ...  \n", "541904  2011-12-09 12:50:00       0.85          XxYFrance  \n", "541905  2011-12-09 12:50:00       2.10             France  \n", "541906  2011-12-09 12:50:00       4.15          XxYFrance  \n", "541907  2011-12-09 12:50:00       4.15          XxYFrance  \n", "541908  2011-12-09 12:50:00       4.95             France  \n", "\n", "[540793 rows x 7 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Removed 'XxY' prefix from Country column. Sample of updated data:\n", "  InvoiceNo StockCode                          Description  Quantity  \\\n", "0    536365    85123A   WHITE HANGING HEART T-LIGHT HOLDER      6.00   \n", "1    536365     71053                  WHITE METAL LANTERN      6.00   \n", "2    536365   ö84406B       CREAM CUPID HEARTS COAT HANGER      8.00   \n", "3    536365    84029G  KNITTED UNION FLAG HOT WATER BOTTLE      6.00   \n", "4    536365    84029E       RED WOOLLY HOTTIE WHITE HEART.      6.00   \n", "\n", "           InvoiceDate  UnitPrice         Country  \n", "0  2010-12-01 08:26:00       2.55  United Kingdom  \n", "1  2010-12-01 08:26:00       3.39  United Kingdom  \n", "2  2010-12-01 08:26:00       2.75  United Kingdom  \n", "3  2010-12-01 08:26:00       3.39  United Kingdom  \n", "4  2010-12-01 08:26:00       3.39  United Kingdom  \n"]}], "source": ["# Remove 'XxY' prefix from Country column\n", "df['Country'] = df['Country'].str.replace('XxY', '')\n", "\n", "print(\"\\nRemoved 'XxY' prefix from Country column. Sample of updated data:\")\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Cleaned StockCode and InvoiceNo columns. Sample of updated data:\n", "  InvoiceNo StockCode                          Description  Quantity  \\\n", "0    536365     85123   WHITE HANGING HEART T-LIGHT HOLDER      6.00   \n", "1    536365     71053                  WHITE METAL LANTERN      6.00   \n", "2    536365     84406       CREAM CUPID HEARTS COAT HANGER      8.00   \n", "3    536365     84029  KNITTED UNION FLAG HOT WATER BOTTLE      6.00   \n", "4    536365     84029       RED WOOLLY HOTTIE WHITE HEART.      6.00   \n", "\n", "           InvoiceDate  UnitPrice         Country  \n", "0  2010-12-01 08:26:00       2.55  United Kingdom  \n", "1  2010-12-01 08:26:00       3.39  United Kingdom  \n", "2  2010-12-01 08:26:00       2.75  United Kingdom  \n", "3  2010-12-01 08:26:00       3.39  United Kingdom  \n", "4  2010-12-01 08:26:00       3.39  United Kingdom  \n"]}], "source": ["# Remove letters from StockCode and symbols from InvoiceNo\n", "df['StockCode'] = df['StockCode'].apply(lambda x: ''.join(char for char in str(x) if char.isdigit()))\n", "df['InvoiceNo'] = df['InvoiceNo'].apply(lambda x: ''.join(char for char in str(x) if char.isdigit()))\n", "\n", "print(\"\\nCleaned StockCode and InvoiceNo columns. Sample of updated data:\")\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InvoiceNo</th>\n", "      <th>StockCode</th>\n", "      <th>Description</th>\n", "      <th>Quantity</th>\n", "      <th>InvoiceDate</th>\n", "      <th>UnitPrice</th>\n", "      <th>Country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>536365</td>\n", "      <td>85123</td>\n", "      <td>WHITE HANGING HEART T-LIGHT HOLDER</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.55</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>536365</td>\n", "      <td>71053</td>\n", "      <td>WHITE METAL LANTERN</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>536365</td>\n", "      <td>84406</td>\n", "      <td>CREAM CUPID HEARTS COAT HANGER</td>\n", "      <td>8.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.75</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>536365</td>\n", "      <td>84029</td>\n", "      <td>KNITTED UNION FLAG HOT WATER BOTTLE</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>536365</td>\n", "      <td>84029</td>\n", "      <td>RED WOOLLY HOTTIE WHITE HEART.</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541904</th>\n", "      <td>581587</td>\n", "      <td>22613</td>\n", "      <td>PACK OF 20 SPACEBOY NAPKINS</td>\n", "      <td>12.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>0.85</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541905</th>\n", "      <td>581587</td>\n", "      <td>22899</td>\n", "      <td>CHILDRENS APRON DOLLY GIRL</td>\n", "      <td>6.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>2.10</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541906</th>\n", "      <td>581587</td>\n", "      <td>23254</td>\n", "      <td>CHILDRENS CUTLERY DOLLY GIRL</td>\n", "      <td>4.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541907</th>\n", "      <td>581587</td>\n", "      <td>23255</td>\n", "      <td>CH<PERSON><PERSON>ENS CUTLERY CIRCUS PARADE</td>\n", "      <td>4.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541908</th>\n", "      <td>581587</td>\n", "      <td>22138</td>\n", "      <td>BAKING SET 9 PIECE RETROSPOT</td>\n", "      <td>3.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.95</td>\n", "      <td>France</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>540793 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       InvoiceNo StockCode                          Description  Quantity  \\\n", "0         536365     85123   WHITE HANGING HEART T-LIGHT HOLDER      6.00   \n", "1         536365     71053                  WHITE METAL LANTERN      6.00   \n", "2         536365     84406       CREAM CUPID HEARTS COAT HANGER      8.00   \n", "3         536365     84029  KNITTED UNION FLAG HOT WATER BOTTLE      6.00   \n", "4         536365     84029       RED WOOLLY HOTTIE WHITE HEART.      6.00   \n", "...          ...       ...                                  ...       ...   \n", "541904    581587     22613          PACK OF 20 SPACEBOY NAPKINS     12.00   \n", "541905    581587     22899           CHILDRENS APRON DOLLY GIRL      6.00   \n", "541906    581587     23254         CHILDRENS CUTLERY DOLLY GIRL      4.00   \n", "541907    581587     23255      CHILDRENS CUTLERY CIRCUS PARADE      4.00   \n", "541908    581587     22138         BAKING SET 9 PIECE RETROSPOT      3.00   \n", "\n", "                InvoiceDate  UnitPrice         Country  \n", "0       2010-12-01 08:26:00       2.55  United Kingdom  \n", "1       2010-12-01 08:26:00       3.39  United Kingdom  \n", "2       2010-12-01 08:26:00       2.75  United Kingdom  \n", "3       2010-12-01 08:26:00       3.39  United Kingdom  \n", "4       2010-12-01 08:26:00       3.39  United Kingdom  \n", "...                     ...        ...             ...  \n", "541904  2011-12-09 12:50:00       0.85          France  \n", "541905  2011-12-09 12:50:00       2.10          France  \n", "541906  2011-12-09 12:50:00       4.15          France  \n", "541907  2011-12-09 12:50:00       4.15          France  \n", "541908  2011-12-09 12:50:00       4.95          France  \n", "\n", "[540793 rows x 7 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First 30 rows of cleaned data:\n", "       InvoiceNo StockCode                         Description  Quantity  \\\n", "541879    581585     22726          ALARM CLOCK BAKELIKE GREEN      8.00   \n", "541880    581585     22727            ALARM CLOCK BAKELIKE RED      4.00   \n", "541881    581585     16016         LARGE CHINESE STYLE SCISSOR     10.00   \n", "541882    581585     21916     SET 12 RETRO WHITE CHALK STICKS     24.00   \n", "541883    581585     84692         BOX OF 24 COCKTAIL PARASOLS     25.00   \n", "541884    581585     84946        ANTIQUE SILVER T-LIGHT GLASS     12.00   \n", "541885    581585     21684     SMALL MEDINA STAMPED METAL BOWL     12.00   \n", "541886    581585     22398          MAGNETS PACK OF 4 SWALLOWS     12.00   \n", "541887    581585     23328  SET 6 SCHOOL MILK BOTTLES IN CRATE      4.00   \n", "541888    581585     23145      ZINC T-LIGHT HOLDER STAR LARGE     12.00   \n", "541889    581585     22466      FAIRY TALE COTTAGE NIGHT LIGHT     12.00   \n", "541890    581586     22061  <PERSON><PERSON><PERSON> CAKE STAND HANGING STRAWBERY      8.00   \n", "541891    581586     23275    SET OF 3 HANGING OWLS OLLIE BEAK     24.00   \n", "541892    581586     21217       RED RETROSPOT ROUND CAKE TINS     24.00   \n", "541893    581586     20685               DOORMAT RED RETROSPOT     10.00   \n", "541894    581587     22631             CIRCUS PARADE LUNCH BOX     12.00   \n", "541895    581587     22556       PLASTERS IN TIN CIRCUS PARADE     12.00   \n", "541896    581587     22555           PLASTERS IN TIN STRONGMAN     12.00   \n", "541897    581587     22728           ALARM CLOCK BAKELIKE PINK      4.00   \n", "541898    581587     22727            ALARM CLOCK BAKELIKE RED      4.00   \n", "541899    581587     22726          ALARM CLOCK BAKELIKE GREEN      4.00   \n", "541900    581587     22730          ALARM CLOCK BAKELIKE IVORY      4.00   \n", "541901    581587     22367     CHILDRENS APRON SPACEBOY DESIGN      8.00   \n", "541902    581587     22629                  SPACEBOY LUNCH BOX     12.00   \n", "541903    581587     23256          <PERSON><PERSON><PERSON>ENS CUTLERY SPACEBOY      4.00   \n", "541904    581587     22613         PACK OF 20 SPACEBOY NAPKINS     12.00   \n", "541905    581587     22899          CHILDRENS APRON DOLLY GIRL      6.00   \n", "541906    581587     23254        CHILDRENS CUTLERY DOLLY GIRL      4.00   \n", "541907    581587     23255     CHILDRENS CUTLERY CIRCUS PARADE      4.00   \n", "541908    581587     22138        BAKING SET 9 PIECE RETROSPOT      3.00   \n", "\n", "                InvoiceDate  UnitPrice         Country  \n", "541879  2011-12-09 12:31:00       3.75  United Kingdom  \n", "541880  2011-12-09 12:31:00       3.75  United Kingdom  \n", "541881  2011-12-09 12:31:00       0.85  United Kingdom  \n", "541882  2011-12-09 12:31:00       0.42  United Kingdom  \n", "541883  2011-12-09 12:31:00       0.42  United Kingdom  \n", "541884  2011-12-09 12:31:00       1.25  United Kingdom  \n", "541885  2011-12-09 12:31:00       0.85  United Kingdom  \n", "541886  2011-12-09 12:31:00       0.39  United Kingdom  \n", "541887  2011-12-09 12:31:00       3.75  United Kingdom  \n", "541888  2011-12-09 12:31:00       0.95  United Kingdom  \n", "541889  2011-12-09 12:31:00       1.95  United Kingdom  \n", "541890  2011-12-09 12:49:00       2.95  United Kingdom  \n", "541891  2011-12-09 12:49:00       1.25  United Kingdom  \n", "541892  2011-12-09 12:49:00       8.95  United Kingdom  \n", "541893  2011-12-09 12:49:00       7.08  United Kingdom  \n", "541894  2011-12-09 12:50:00       1.95          France  \n", "541895  2011-12-09 12:50:00       1.65          France  \n", "541896  2011-12-09 12:50:00       1.65          France  \n", "541897  2011-12-09 12:50:00       3.75          France  \n", "541898  2011-12-09 12:50:00       3.75          France  \n", "541899  2011-12-09 12:50:00       3.75          France  \n", "541900  2011-12-09 12:50:00       3.75          France  \n", "541901  2011-12-09 12:50:00       1.95          France  \n", "541902  2011-12-09 12:50:00       1.95          France  \n", "541903  2011-12-09 12:50:00       4.15          France  \n", "541904  2011-12-09 12:50:00       0.85          France  \n", "541905  2011-12-09 12:50:00       2.10          France  \n", "541906  2011-12-09 12:50:00       4.15          France  \n", "541907  2011-12-09 12:50:00       4.15          France  \n", "541908  2011-12-09 12:50:00       4.95          France  \n"]}], "source": ["# Display first 30 rows of the cleaned dataframe\n", "print(\"\\nFirst 30 rows of cleaned data:\")\n", "print(df.tail(30))\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized data format. Sample of updated data:\n", "  InvoiceNo StockCode                          Description  Quantity  \\\n", "0    536365     85123   White Hanging Heart T-Light Holder      6.00   \n", "1    536365     71053                  White Metal Lantern      6.00   \n", "2    536365     84406       Cream Cupid Hearts Coat Hanger      8.00   \n", "3    536365     84029  Knitted Union Flag Hot Water Bottle      6.00   \n", "4    536365     84029       <PERSON> Woolly <PERSON> White Heart.      6.00   \n", "\n", "          InvoiceDate  UnitPrice         Country  \n", "0 2010-12-01 08:26:00       2.55  United Kingdom  \n", "1 2010-12-01 08:26:00       3.39  United Kingdom  \n", "2 2010-12-01 08:26:00       2.75  United Kingdom  \n", "3 2010-12-01 08:26:00       3.39  United Kingdom  \n", "4 2010-12-01 08:26:00       3.39  United Kingdom  \n"]}], "source": ["# Remove emojis and special characters from Description and Country\n", "df['Description'] = df['Description'].str.replace('$', '')  # Remove dollar signs\n", "df['Description'] = df['Description'].str.replace('@', '')  # Remove @ symbols\n", "df['Country'] = df['Country'].str.replace('☺️', '')  # Remove emojis\n", "\n", "# Standardize case in Description and Country\n", "df['Description'] = df['Description'].str.strip()\n", "df['Description'] = df['Description'].str.title()\n", "df['Country'] = df['Country'].str.strip() \n", "df['Country'] = df['Country'].str.title()\n", "\n", "# Format numeric columns\n", "df['Quantity'] = df['Quantity'].round(2)  # Round to 2 decimal places\n", "df['UnitPrice'] = df['UnitPrice'].round(2)  # Round to 2 decimal places\n", "\n", "# Format InvoiceDate as datetime\n", "df['InvoiceDate'] = pd.to_datetime(df['InvoiceDate'])\n", "\n", "print(\"\\nStandardized data format. Sample of updated data:\")\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InvoiceNo</th>\n", "      <th>StockCode</th>\n", "      <th>Description</th>\n", "      <th>Quantity</th>\n", "      <th>InvoiceDate</th>\n", "      <th>UnitPrice</th>\n", "      <th>Country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>536365</td>\n", "      <td>85123</td>\n", "      <td><PERSON> Hanging Heart T-Light Holder</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.55</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>536365</td>\n", "      <td>71053</td>\n", "      <td>White Metal Lantern</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>536365</td>\n", "      <td>84406</td>\n", "      <td>Cream Cupid Hearts Coat Hanger</td>\n", "      <td>8.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>2.75</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>536365</td>\n", "      <td>84029</td>\n", "      <td>Knitted Union Flag Hot Water Bottle</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>536365</td>\n", "      <td>84029</td>\n", "      <td><PERSON>ly <PERSON>.</td>\n", "      <td>6.00</td>\n", "      <td>2010-12-01 08:26:00</td>\n", "      <td>3.39</td>\n", "      <td>United Kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541904</th>\n", "      <td>581587</td>\n", "      <td>22613</td>\n", "      <td>Pack Of 20 Spaceboy Napkins</td>\n", "      <td>12.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>0.85</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541905</th>\n", "      <td>581587</td>\n", "      <td>22899</td>\n", "      <td>Childrens Apron <PERSON> Girl</td>\n", "      <td>6.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>2.10</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541906</th>\n", "      <td>581587</td>\n", "      <td>23254</td>\n", "      <td>Childrens Cutlery Dolly Girl</td>\n", "      <td>4.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541907</th>\n", "      <td>581587</td>\n", "      <td>23255</td>\n", "      <td>Childrens Cutlery Circus Parade</td>\n", "      <td>4.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.15</td>\n", "      <td>France</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541908</th>\n", "      <td>581587</td>\n", "      <td>22138</td>\n", "      <td>Baking Set 9 Piece Retrospot</td>\n", "      <td>3.00</td>\n", "      <td>2011-12-09 12:50:00</td>\n", "      <td>4.95</td>\n", "      <td>France</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>540793 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       InvoiceNo StockCode                          Description  Quantity  \\\n", "0         536365     85123   White Hanging Heart T-Light Holder      6.00   \n", "1         536365     71053                  White Metal Lantern      6.00   \n", "2         536365     84406       Cream Cupid Hearts Coat Hanger      8.00   \n", "3         536365     84029  Knitted Union Flag Hot Water Bottle      6.00   \n", "4         536365     84029       <PERSON> Woolly <PERSON> White Heart.      6.00   \n", "...          ...       ...                                  ...       ...   \n", "541904    581587     22613          Pack Of 20 Spaceboy Napkins     12.00   \n", "541905    581587     22899           Childrens Apron Dolly Girl      6.00   \n", "541906    581587     23254         Childrens Cutlery Dolly Girl      4.00   \n", "541907    581587     23255      Childrens Cutlery Circus Parade      4.00   \n", "541908    581587     22138         Baking Set 9 Piece Retrospot      3.00   \n", "\n", "               InvoiceDate  UnitPrice         Country  \n", "0      2010-12-01 08:26:00       2.55  United Kingdom  \n", "1      2010-12-01 08:26:00       3.39  United Kingdom  \n", "2      2010-12-01 08:26:00       2.75  United Kingdom  \n", "3      2010-12-01 08:26:00       3.39  United Kingdom  \n", "4      2010-12-01 08:26:00       3.39  United Kingdom  \n", "...                    ...        ...             ...  \n", "541904 2011-12-09 12:50:00       0.85          France  \n", "541905 2011-12-09 12:50:00       2.10          France  \n", "541906 2011-12-09 12:50:00       4.15          France  \n", "541907 2011-12-09 12:50:00       4.15          France  \n", "541908 2011-12-09 12:50:00       4.95          France  \n", "\n", "[540793 rows x 7 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Cleaned dataset has been saved to 'data/dataset/cleaned_dataset.csv'\n"]}], "source": ["# Save cleaned dataset to CSV\n", "df.to_csv('data/dataset/cleaned_dataset.csv', index=False)\n", "print(\"\\nCleaned dataset has been saved to 'data/dataset/cleaned_dataset.csv'\")\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Data types of each column:\n", "InvoiceNo              object\n", "StockCode              object\n", "Description            object\n", "Quantity              float64\n", "InvoiceDate    datetime64[ns]\n", "UnitPrice             float64\n", "Country                object\n", "dtype: object\n"]}], "source": ["# Check the data types of each column in the dataframe\n", "print(\"\\nData types of each column:\")\n", "print(df.dtypes)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}