import os
import requests
import pandas as pd
import time
import logging
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image
import io
import hashlib

class WebScrapingService:
    """
    Service for scraping product images from e-commerce websites.
    Efficiently downloads and stores images for CNN model training.
    """
    
    def __init__(self, output_dir: str = "data/scraped_images"):
        """
        Initialize the web scraping service.
        
        Args:
            output_dir (str): Directory to store scraped images
        """
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Create output directory
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize session for requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Initialize Selenium driver
        self.driver = None
        self._setup_selenium()
        
        # Scraping statistics
        self.stats = {
            'total_attempted': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'errors': []
        }
    
    def _setup_selenium(self):
        """Setup Selenium WebDriver for dynamic content scraping."""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            # Use webdriver-manager to handle driver installation
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.logger.info("Selenium WebDriver initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Selenium: {str(e)}")
            self.driver = None
    
    def generate_stock_codes(self, count: int = 20000, start_from: int = 20000) -> List[str]:
        """
        Generate unique stock codes for CNN training data starting from specified number.
        
        Args:
            count (int): Number of unique stock codes to generate
            start_from (int): Starting number for stock codes
            
        Returns:
            List[str]: List of unique stock codes
        """
        stock_codes = []
        for i in range(start_from, start_from + count):
            # Generate unique stock code format: 20000, 20001, 20002, etc.
            stock_code = str(i)
            stock_codes.append(stock_code)
        
        self.logger.info(f"Generated {len(stock_codes)} unique stock codes starting from {start_from}")
        return stock_codes
    
    def search_product_images(self, stock_code: str, product_name: str = None) -> List[str]:
        """
        Search for product images using stock code and optional product name.
        
        Args:
            stock_code (str): Product stock code
            product_name (str, optional): Product name for better search results
            
        Returns:
            List[str]: List of image URLs found
        """
        image_urls = []
        
        # Search terms to try
        search_terms = []
        if product_name:
            search_terms.append(product_name)
        search_terms.append(stock_code)
        
        for search_term in search_terms:
            try:
                # Search on multiple e-commerce sites
                urls = self._search_amazon(search_term)
                image_urls.extend(urls)
                
                urls = self._search_ebay(search_term)
                image_urls.extend(urls)
                
                urls = self._search_google_images(search_term)
                image_urls.extend(urls)
                
            except Exception as e:
                self.logger.error(f"Error searching for {search_term}: {str(e)}")
        
        # Remove duplicates while preserving order
        unique_urls = []
        seen = set()
        for url in image_urls:
            if url not in seen:
                unique_urls.append(url)
                seen.add(url)
        
        return unique_urls[:5]  # Limit to top 5 results
    
    def _search_amazon(self, search_term: str) -> List[str]:
        """Search for product images on Amazon."""
        try:
            search_url = f"https://www.amazon.com/s?k={search_term.replace(' ', '+')}"
            
            if self.driver:
                self.driver.get(search_url)
                time.sleep(2)
                
                # Wait for images to load
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "img[src*='images-na.ssl-images-amazon.com']"))
                )
                
                # Find product images
                images = self.driver.find_elements(By.CSS_SELECTOR, "img[src*='images-na.ssl-images-amazon.com']")
                urls = [img.get_attribute('src') for img in images if img.get_attribute('src')]
                
                return urls[:3]  # Limit to 3 results
            else:
                # Fallback to requests
                response = self.session.get(search_url, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')
                images = soup.find_all('img', src=lambda x: x and 'images-na.ssl-images-amazon.com' in x)
                return [img['src'] for img in images[:3]]
                
        except Exception as e:
            self.logger.error(f"Error searching Amazon: {str(e)}")
            return []
    
    def _search_ebay(self, search_term: str) -> List[str]:
        """Search for product images on eBay."""
        try:
            search_url = f"https://www.ebay.com/sch/i.html?_nkw={search_term.replace(' ', '+')}"
            
            if self.driver:
                self.driver.get(search_url)
                time.sleep(2)
                
                # Wait for images to load
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "img[src*='i.ebayimg.com']"))
                )
                
                # Find product images
                images = self.driver.find_elements(By.CSS_SELECTOR, "img[src*='i.ebayimg.com']")
                urls = [img.get_attribute('src') for img in images if img.get_attribute('src')]
                
                return urls[:3]  # Limit to 3 results
            else:
                # Fallback to requests
                response = self.session.get(search_url, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')
                images = soup.find_all('img', src=lambda x: x and 'i.ebayimg.com' in x)
                return [img['src'] for img in images[:3]]
                
        except Exception as e:
            self.logger.error(f"Error searching eBay: {str(e)}")
            return []
    
    def _search_google_images(self, search_term: str) -> List[str]:
        """Search for product images on Google Images."""
        try:
            search_url = f"https://www.google.com/search?q={search_term.replace(' ', '+')}&tbm=isch"
            
            if self.driver:
                self.driver.get(search_url)
                time.sleep(2)
                
                # Wait for images to load
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "img"))
                )
                
                # Find product images
                images = self.driver.find_elements(By.CSS_SELECTOR, "img")
                urls = [img.get_attribute('src') for img in images if img.get_attribute('src') and img.get_attribute('src').startswith('http')]
                
                return urls[:3]  # Limit to 3 results
            else:
                # Fallback to requests
                response = self.session.get(search_url, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')
                images = soup.find_all('img')
                return [img['src'] for img in images if img.get('src') and img['src'].startswith('http')][:3]
                
        except Exception as e:
            self.logger.error(f"Error searching Google Images: {str(e)}")
            return []
    
    def download_image(self, url: str, stock_code: str, index: int = 0) -> Optional[str]:
        """
        Download image from URL and save it locally.
        
        Args:
            url (str): Image URL to download
            stock_code (str): Stock code for naming
            index (int): Index for multiple images of same product
            
        Returns:
            Optional[str]: Path to saved image, None if failed
        """
        try:
            # Create stock code directory
            stock_dir = os.path.join(self.output_dir, stock_code)
            os.makedirs(stock_dir, exist_ok=True)
            
            # Download image
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            # Validate image
            image = Image.open(io.BytesIO(response.content))
            
            # Check minimum size (avoid thumbnails)
            if image.size[0] < 100 or image.size[1] < 100:
                self.logger.warning(f"Image too small for {stock_code}: {image.size}")
                return None
            
            # Generate filename
            filename = f"{stock_code}_{index}.jpg"
            filepath = os.path.join(stock_dir, filename)
            
            # Save image
            image.save(filepath, 'JPEG', quality=85)
            
            self.logger.info(f"Downloaded image for {stock_code}: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error downloading image from {url}: {str(e)}")
            return None
    
    def scrape_product_images(self, stock_codes: List[str] = None, max_per_product: int = 1) -> Dict[str, Any]:
        """
        Scrape product images for all stock codes and update CNN_Model_Train_Data.csv.
        
        Args:
            stock_codes (List[str], optional): List of stock codes to scrape
            max_per_product (int): Maximum images to download per product
            
        Returns:
            Dict[str, Any]: Scraping statistics and results
        """
        if stock_codes is None:
            stock_codes = self.generate_stock_codes(20000, start_from=20000)
        
        results = {
            'total_products': len(stock_codes),
            'successful_products': 0,
            'failed_products': 0,
            'total_images_downloaded': 0,
            'product_results': {}
        }
        
        # Prepare data for CSV
        csv_data = []
        
        for i, stock_code in enumerate(stock_codes):
            self.logger.info(f"Processing product {i+1}/{len(stock_codes)}: {stock_code}")
            
            try:
                # Search for images
                image_urls = self.search_product_images(stock_code)
                
                if not image_urls:
                    self.logger.warning(f"No images found for {stock_code}")
                    results['failed_products'] += 1
                    results['product_results'][stock_code] = {'status': 'no_images_found', 'images': []}
                    # Add to CSV with empty image path
                    csv_data.append({'stockcode': stock_code, 'image': ''})
                    continue
                
                # Download first image (max_per_product=1 for CSV storage)
                downloaded_image = None
                for j, url in enumerate(image_urls[:max_per_product]):
                    filepath = self.download_image(url, stock_code, j)
                    if filepath:
                        downloaded_image = filepath
                        results['total_images_downloaded'] += 1
                        break
                
                if downloaded_image:
                    results['successful_products'] += 1
                    results['product_results'][stock_code] = {
                        'status': 'success',
                        'images': [downloaded_image],
                        'count': 1
                    }
                    # Add to CSV with relative image path
                    relative_path = os.path.relpath(downloaded_image, start=os.getcwd())
                    csv_data.append({'stockcode': stock_code, 'image': relative_path})
                else:
                    results['failed_products'] += 1
                    results['product_results'][stock_code] = {'status': 'download_failed', 'images': []}
                    # Add to CSV with empty image path
                    csv_data.append({'stockcode': stock_code, 'image': ''})
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error processing {stock_code}: {str(e)}")
                results['failed_products'] += 1
                results['product_results'][stock_code] = {'status': 'error', 'error': str(e), 'images': []}
                # Add to CSV with empty image path
                csv_data.append({'stockcode': stock_code, 'image': ''})
        
        # Save results to CSV
        self._save_to_csv(csv_data)
        
        # Save detailed results
        self._save_scraping_results(results)
        
        return results
    
    def _save_to_csv(self, data: List[Dict[str, str]]):
        """Save stock codes and image paths to CNN_Model_Train_Data.csv."""
        try:
            df = pd.DataFrame(data)
            csv_path = "data/dataset/CNN_Model_Train_Data.csv"
            df.to_csv(csv_path, index=False)
            self.logger.info(f"Saved {len(data)} records to {csv_path}")
        except Exception as e:
            self.logger.error(f"Error saving to CSV: {str(e)}")
    
    def _save_scraping_results(self, results: Dict[str, Any]):
        """Save scraping results to JSON file."""
        try:
            import json
            results_file = os.path.join(self.output_dir, 'scraping_results.json')
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            self.logger.info(f"Scraping results saved to {results_file}")
        except Exception as e:
            self.logger.error(f"Error saving results: {str(e)}")
    
    def get_scraping_stats(self) -> Dict[str, Any]:
        """Get current scraping statistics."""
        return {
            'output_directory': self.output_dir,
            'total_files': len([f for f in os.listdir(self.output_dir) if os.path.isfile(os.path.join(self.output_dir, f))]),
            'total_directories': len([d for d in os.listdir(self.output_dir) if os.path.isdir(os.path.join(self.output_dir, d))]),
            'scraping_stats': self.stats
        }
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            self.driver.quit()
            self.logger.info("Selenium WebDriver closed")
        
        if self.session:
            self.session.close()
            self.logger.info("Requests session closed") 