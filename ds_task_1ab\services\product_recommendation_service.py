import pandas as pd
from .embedding_service import EmbeddingService
from .vector_store import VectorStore

class ProductRecommendationService:
    def __init__(self):
        """Initialize the recommendation service with required components."""
        print("Initializing recommendation service...")
        self.embedding_service = EmbeddingService()
        print("Embedding service initialized")
        self.vector_store = VectorStore()
        print("Vector store initialized")
        
        # Load and prepare product data
        print("Loading product data...")
        self.load_product_data()
        print("Product data loaded")
    
    def load_product_data(self):
        """Load product data from CSV and prepare it for recommendations."""
        try:
            print("Loading product data from CSV...")
            df = pd.read_csv('data/dataset/cleaned_dataset.csv')
            print(f"Loaded {len(df)} rows from CSV")
            
            # Group by StockCode to get unique products
            self.products_df = df.groupby('StockCode').agg({
                'Description': 'first',
                'UnitPrice': 'mean'
            }).reset_index()
            print(f"Found {len(self.products_df)} unique products")
            
            # Generate embeddings for all product descriptions
            print("Generating embeddings for product descriptions...")
            descriptions = self.products_df['Description'].tolist()
            embeddings = self.embedding_service.create_embeddings_batch(descriptions)
            print(f"Generated {len(embeddings)} embeddings")
            
            # Prepare metadata for vector store
            print("Preparing metadata...")
            metadata = []
            for _, row in self.products_df.iterrows():
                metadata.append({
                    'stock_code': str(row['StockCode']),
                    'description': row['Description'],
                    'unit_price': float(row['UnitPrice'])
                })
            print(f"Prepared metadata for {len(metadata)} products")
            
            # Upload to vector store
            print("Uploading vectors to Pinecone...")
            self.vector_store.upsert_vectors(embeddings.tolist(), metadata)
            print("Product data loaded successfully")
            
        except Exception as e:
            raise Exception(f"Failed to load product data: {str(e)}")
    
    def get_recommendations(self, query, top_k=None, min_score=0.3, price_range=None):
        """Get product recommendations based on a natural language query.
        
        Args:
            query (str): Natural language query from the user
            top_k (int, optional): Maximum number of recommendations to return. If None, returns all found results.
            min_score (float): Minimum similarity score (0.0 to 1.0)
            price_range (tuple, optional): (min_price, max_price) for filtering
            
        Returns:
            tuple: (list of product dictionaries, natural language response)
        """
        try:
            # Generate embedding for the query
            query_embedding = self.embedding_service.create_embedding(query)
            
            # Prepare metadata filter if price range is provided
            filter_dict = None
            if price_range:
                min_price, max_price = price_range
                filter_dict = {
                    "unit_price": {
                        "$gte": float(min_price),
                        "$lte": float(max_price)
                    }
                }
            
            # Use a reasonable default for top_k if not specified, but allow flexibility
            search_top_k = top_k if top_k is not None else 50  # Higher limit to get more results
            
            # Search for similar products
            results = self.vector_store.search(
                query_vector=query_embedding.tolist(),
                top_k=search_top_k,
                filter_dict=filter_dict,
                score_threshold=min_score
            )
            
            # Format results - only include product details, no similarity scores
            products = []
            for _, score, metadata in results:
                products.append({
                    'stock_code': metadata['stock_code'],
                    'description': metadata['description'],
                    'unit_price': metadata['unit_price']
                })
            
            # Generate natural language response
            if products:
                response = f"I found {len(products)} products that match your query."
                
                if price_range:
                    response += f" Results are filtered to price range: £{min_price:.2f} - £{max_price:.2f}."
            else:
                response = "I couldn't find any products matching your query"
                if price_range:
                    response += f" in the price range £{min_price:.2f} - £{max_price:.2f}"
                response += ". Please try different search terms or adjust your filters."
            
            return products, response
            
        except Exception as e:
            raise Exception(f"Error getting recommendations: {str(e)}") 