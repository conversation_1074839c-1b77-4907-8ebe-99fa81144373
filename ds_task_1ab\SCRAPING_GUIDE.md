# Enhanced Web Scraping Guide for Task 5

This guide explains how to use the enhanced web scraping scripts for collecting product images for CNN model training.

## Files Overview

1. **`simple_scraping.py`** - Enhanced version of the original script with multi-category support
2. **`enhanced_scraping.py`** - Class-based implementation with better error handling and filtering

## Features

### ✅ Enhanced Image Filtering
- Filters out icons, logos, UI elements, and non-product images
- Only downloads actual product images suitable for CNN training
- Checks image size to avoid tiny thumbnails
- Validates image extensions (.jpg, .jpeg, .png, .webp)

### ✅ Multi-Product Support
- Can scrape multiple product categories in one run
- Configurable number of products per category
- Configurable number of images per product
- Organized directory structure

### ✅ Robust Error Handling
- Handles network timeouts and connection errors
- Graceful handling of missing elements
- Continues scraping even if individual products fail
- Detailed logging and progress reporting

### ✅ Data Organization
- Creates organized directory structure
- Generates CSV file for CNN training (`CNN_Model_Train_Data.csv`)
- Includes stock codes, product names, and image paths
- Compatible with the project's data requirements

## Quick Start

### Option 1: Using enhanced_scraping.py (Recommended)

```bash
# Navigate to the project directory
cd ds_task_1ab

# Run the enhanced scraper
python enhanced_scraping.py
```

**Interactive Setup:**
1. Choose mode: Single product (1) or Multiple products (2)
2. For single product: Enter the product name to search
3. For multiple products: Uses predefined categories

### Option 2: Using simple_scraping.py

```bash
# Navigate to the project directory
cd ds_task_1ab

# Run the simple scraper
python simple_scraping.py
```

## Configuration

### Chrome WebDriver Setup

**Important:** Update the ChromeDriver path in the scripts:

```python
# In enhanced_scraping.py, update this line:
CHROMEDRIVER_PATH = r'C:\path\to\your\chromedriver.exe'

# Or set it to None if chromedriver is in your PATH
CHROMEDRIVER_PATH = None
```

### Customizing Product Categories

Edit the `PRODUCT_CATEGORIES` list in either script:

```python
PRODUCT_CATEGORIES = [
    "laptop", "smartphone", "headphones", "camera", "tablet",
    "smartwatch", "wireless mouse", "keyboard", "monitor", "speaker",
    "shoes", "bag", "sunglasses", "books", "toys"
]
```

### Adjusting Scraping Parameters

```python
# Number of products to scrape per category
products_per_category = 5

# Number of images to download per product
images_per_product = 3

# Starting stock code for naming
start_stock_code = 20000
```

## Output Structure

```
data/
├── scraped_images/
│   ├── laptop/
│   │   ├── 20000/
│   │   │   ├── 20000_0.jpg
│   │   │   ├── 20000_1.jpg
│   │   │   └── 20000_2.jpg
│   │   ├── 20001/
│   │   └── ...
│   ├── smartphone/
│   └── ...
└── dataset/
    └── CNN_Model_Train_Data.csv
```

## CSV Output Format

The generated `CNN_Model_Train_Data.csv` contains:

| Column | Description |
|--------|-------------|
| stockcode | Unique identifier for each product |
| product_name | Name/title of the product |
| image | Relative path to the downloaded image |

Example:
```csv
stockcode,product_name,image
20000,Dell Inspiron 15 3000 Laptop,data/scraped_images/laptop/20000/20000_0.jpg
20000,Dell Inspiron 15 3000 Laptop,data/scraped_images/laptop/20000/20000_1.jpg
20001,HP Pavilion Gaming Laptop,data/scraped_images/laptop/20001/20001_0.jpg
```

## Best Practices

### 🔧 Performance Optimization
- Start with small numbers for testing (5 products, 2 images each)
- Increase gradually for production runs
- Use headless mode for faster scraping
- Add delays between requests to be respectful

### 🛡️ Error Prevention
- Ensure stable internet connection
- Keep ChromeDriver updated
- Monitor disk space for image storage
- Check Amazon's robots.txt and terms of service

### 📊 Data Quality
- Review downloaded images manually
- Remove any non-product images that slipped through
- Ensure diverse product representation
- Balance dataset across categories

## Troubleshooting

### Common Issues

**ChromeDriver not found:**
```bash
# Install webdriver-manager for automatic driver management
pip install webdriver-manager

# Or download ChromeDriver manually from:
# https://chromedriver.chromium.org/
```

**No images downloaded:**
- Check internet connection
- Verify ChromeDriver path
- Try running in non-headless mode to see what's happening
- Check if Amazon has changed their page structure

**Images are too small/low quality:**
- Adjust the minimum size filter in `is_valid_product_image()`
- Increase the size threshold from 150 to a higher value

**Rate limiting/blocking:**
- Increase delays between requests
- Use different User-Agent strings
- Consider using proxy rotation for large-scale scraping

## Advanced Usage

### Custom Image Filtering

Modify the `is_valid_product_image()` function to add custom filters:

```python
def is_valid_product_image(self, img_url, alt_text=None):
    # Add your custom filtering logic here
    # Example: Only accept images larger than 300px
    size_matches = re.findall(r'_(?:SL|AC_|UL|SR)(\d+)', img_url)
    if size_matches:
        max_size = max(int(size) for size in size_matches)
        if max_size < 300:  # Custom threshold
            return False
    return True
```

### Batch Processing

For large-scale scraping, consider running in batches:

```python
# Process categories in smaller batches
categories_batch1 = ["laptop", "smartphone", "tablet"]
categories_batch2 = ["camera", "headphones", "smartwatch"]

# Run each batch separately with delays
```

## Legal and Ethical Considerations

- ⚖️ Respect website terms of service
- 🤖 Follow robots.txt guidelines
- ⏱️ Use reasonable delays between requests
- 📚 Use scraped data only for educational/research purposes
- 🔄 Consider using official APIs when available

## Support

If you encounter issues:
1. Check the console output for error messages
2. Verify all dependencies are installed
3. Ensure ChromeDriver is compatible with your Chrome version
4. Try running with a single product first to isolate issues

For more help, refer to the main project README.md or create an issue in the project repository.
